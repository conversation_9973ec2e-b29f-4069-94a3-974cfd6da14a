# دليل قاعدة بيانات تطبيق مراسِل
## Morasel Database Documentation

### نظرة عامة
قاعدة البيانات مصممة لدعم تطبيق مراسِل - منصة التواصل الداخلي للمؤسسات. تتضمن 30+ جدول لإدارة جميع جوانب التطبيق من المراسلة إلى الأمان والإدارة.

---

## هيكل قاعدة البيانات

### 1. الجداول الأساسية

#### `departments` - الأقسام
- إدارة هيكل الأقسام في المؤسسة
- دعم الأقسام الفرعية (parent-child relationship)
- ربط كل قسم بمدير

#### `users` - المستخدمون
- معلومات المستخدمين الأساسية
- نظا<PERSON> الأدوار (admin, department_manager, administrator, employee)
- ر<PERSON><PERSON> بالأق<PERSON>ا<PERSON> والمناصب

#### `user_settings` - إعدادات المستخدمين
- إعدادات قابلة للتخصيص لكل مستخدم
- نظام key-value مرن

### 2. جداول الأمان والمصادقة

#### `password_resets` - إعادة تعيين كلمات المرور
- إدارة طلبات استعادة كلمة المرور
- رموز مؤقتة مع انتهاء صلاحية

#### `login_attempts` - محاولات تسجيل الدخول
- تسجيل جميع محاولات الدخول (ناجحة وفاشلة)
- حماية من الهجمات المتكررة

#### `user_sessions` - جلسات المستخدمين
- إدارة جلسات تسجيل الدخول
- معلومات الجهاز والموقع

#### `two_factor_auth` - التحقق بخطوتين
- إعدادات 2FA لكل مستخدم
- رموز احتياطية

### 3. جداول المراسلة

#### `groups` - المجموعات
- مجموعات الدردشة (أقسام، مشاريع، فرق)
- إعدادات الخصوصية والحد الأقصى للأعضاء

#### `group_members` - أعضاء المجموعات
- عضوية المجموعات مع الأدوار
- تتبع من أضاف العضو ومتى

#### `conversations` - المحادثات
- محادثات فردية وجماعية
- ربط بآخر رسالة ووقت النشاط

#### `conversation_participants` - مشاركو المحادثات
- المشاركون في المحادثات المباشرة
- تتبع آخر قراءة

#### `messages` - الرسائل
- جميع أنواع الرسائل (نص، صور، ملفات، صوت)
- دعم الردود والتعديل والجدولة
- إمكانية الحذف الناعم

#### `message_reads` - قراءة الرسائل
- تتبع من قرأ كل رسالة ومتى

#### `message_reactions` - ردود الفعل
- إعجابات وردود فعل على الرسائل

#### `mentions` - الإشارات
- إشارات المستخدمين في الرسائل (@username)

### 4. جداول الاجتماعات

#### `meetings` - الاجتماعات
- جدولة وإدارة الاجتماعات
- دعم الاجتماعات المتكررة
- أنواع مختلفة (خاصة، قسم، عامة)

#### `meeting_participants` - مشاركو الاجتماعات
- دعوات وحضور الاجتماعات
- تتبع حالة الدعوة والحضور

### 5. جداول البث المباشر

#### `live_streams` - البث المباشر
- إدارة جلسات البث المباشر
- إحصائيات المشاهدة والتسجيل

#### `stream_viewers` - مشاهدو البث
- تتبع المشاهدين ووقت المشاهدة

#### `stream_messages` - رسائل البث
- رسائل التفاعل أثناء البث المباشر

### 6. جداول المهام والإشعارات

#### `tasks` - المهام
- مهام مرتبطة بالمحادثات والرسائل
- أولويات وحالات مختلفة

#### `notifications` - الإشعارات
- جميع أنواع الإشعارات
- تتبع القراءة وإرسال Push

#### `notification_settings` - إعدادات الإشعارات
- تخصيص إعدادات الإشعارات لكل مستخدم

### 7. جداول الملفات

#### `files` - الملفات
- إدارة جميع الملفات المرفوعة
- معلومات تفصيلية وإحصائيات التحميل

#### `file_shares` - مشاركة الملفات
- إدارة صلاحيات الوصول للملفات
- مشاركة مع مستخدمين أو مجموعات

### 8. جداول النظام

#### `backups` - النسخ الاحتياطية
- إدارة عمليات النسخ الاحتياطي
- أنواع مختلفة من النسخ

#### `activity_logs` - سجل النشاطات
- تسجيل جميع أنشطة المستخدمين
- مراجعة وتدقيق النظام

#### `permissions` - الصلاحيات
- تعريف جميع الصلاحيات في النظام

#### `role_permissions` - صلاحيات الأدوار
- ربط الصلاحيات بالأدوار

#### `user_permissions` - صلاحيات المستخدمين
- صلاحيات مخصصة للمستخدمين

#### `system_settings` - إعدادات النظام
- إعدادات عامة للتطبيق

---

## الفهارس والأداء

### الفهارس الأساسية
- فهارس على المفاتيح الأساسية والخارجية
- فهارس على الحقول المستخدمة في البحث كثيراً

### الفهارس المركبة
```sql
-- للرسائل حسب المحادثة والتاريخ
CREATE INDEX idx_messages_conversation_sent ON messages(conversation_id, sent_at DESC);

-- للإشعارات غير المقروءة
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, is_read, created_at DESC);

-- لسجل النشاطات
CREATE INDEX idx_activity_logs_user_date ON activity_logs(user_id, created_at DESC);
```

### المشاهدات (Views)

#### `conversation_summary`
- ملخص المحادثات مع آخر رسالة
- يسهل عرض قائمة المحادثات

#### `unread_notifications`
- الإشعارات غير المقروءة
- تحسين أداء عرض الإشعارات

---

## الأمان

### تشفير البيانات
- كلمات المرور مشفرة باستخدام bcrypt
- رموز الجلسات آمنة
- رموز 2FA محمية

### التدقيق
- تسجيل جميع العمليات في `activity_logs`
- تتبع محاولات تسجيل الدخول
- سجل تغييرات الصلاحيات

### النسخ الاحتياطي
- نسخ دورية تلقائية
- تشفير النسخ الاحتياطية
- إمكانية الاسترجاع الانتقائي

---

## التحسينات المستقبلية

### الأداء
- تقسيم الجداول الكبيرة (Partitioning)
- تحسين الاستعلامات المعقدة
- إضافة فهارس حسب الحاجة

### الميزات
- دعم الرسائل المؤقتة
- تشفير end-to-end للرسائل
- أرشفة الرسائل القديمة

### المراقبة
- إحصائيات الأداء
- تنبيهات النظام
- تقارير الاستخدام

---

## استخدام قاعدة البيانات

### إنشاء قاعدة البيانات
```bash
# تشغيل سكريبت إنشاء قاعدة البيانات
mysql -u root -p < database/schema.sql
```

### النسخ الاحتياطي
```bash
# إنشاء نسخة احتياطية
mysqldump -u root -p morasel_db > backup_$(date +%Y%m%d).sql
```

### الاستعادة
```bash
# استعادة من نسخة احتياطية
mysql -u root -p morasel_db < backup_20250712.sql
```

---

## ملاحظات مهمة

1. **الأداء**: استخدم الفهارس بحكمة لتحسين الأداء
2. **الأمان**: لا تخزن كلمات المرور بشكل واضح أبداً
3. **النسخ الاحتياطي**: قم بنسخ احتياطية دورية
4. **المراقبة**: راقب أداء الاستعلامات باستمرار
5. **التحديث**: حدث الإحصائيات بانتظام

---

*هذا الدليل قابل للتحديث مع تطور التطبيق*
