const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');
const admin = require('firebase-admin');

const User = require('../models/User');
const LoginAttempt = require('../models/LoginAttempt');
const PasswordReset = require('../models/PasswordReset');
const UserSession = require('../models/UserSession');
const { sendEmail } = require('../services/emailService');
const { generateToken, verifyToken } = require('../utils/tokenUtils');
const { getClientInfo } = require('../utils/requestUtils');
const logger = require('../utils/logger');

/**
 * تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
 */
const signInWithEmailAndPassword = async (req, res) => {
  try {
    // التحقق من صحة البيانات
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { email, password, rememberMe = false } = req.body;
    const clientInfo = getClientInfo(req);

    // البحث عن المستخدم
    const user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: ['department']
    });

    // تسجيل محاولة تسجيل الدخول
    const loginAttempt = {
      email: email.toLowerCase(),
      ipAddress: clientInfo.ipAddress,
      userAgent: clientInfo.userAgent,
      success: false,
      userId: user?.id || null
    };

    if (!user) {
      await LoginAttempt.create({
        ...loginAttempt,
        failureReason: 'user_not_found'
      });
      
      return res.status(401).json({
        success: false,
        message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من حالة المستخدم
    if (!user.isActive) {
      await LoginAttempt.create({
        ...loginAttempt,
        failureReason: 'user_disabled'
      });
      
      return res.status(401).json({
        success: false,
        message: 'تم إلغاء تفعيل حسابك. يرجى التواصل مع الإدارة'
      });
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      await LoginAttempt.create({
        ...loginAttempt,
        failureReason: 'wrong_password'
      });
      
      return res.status(401).json({
        success: false,
        message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من محاولات تسجيل الدخول المتكررة
    const recentFailedAttempts = await LoginAttempt.count({
      where: {
        email: email.toLowerCase(),
        success: false,
        timestamp: {
          [Op.gte]: new Date(Date.now() - 15 * 60 * 1000) // آخر 15 دقيقة
        }
      }
    });

    if (recentFailedAttempts >= 5) {
      await LoginAttempt.create({
        ...loginAttempt,
        failureReason: 'too_many_attempts'
      });
      
      return res.status(429).json({
        success: false,
        message: 'تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى بعد 15 دقيقة'
      });
    }

    // إنشاء JWT token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    const expiresIn = rememberMe ? '30d' : '24h';
    const token = generateToken(tokenPayload, expiresIn);

    // إنشاء جلسة جديدة
    const session = await UserSession.create({
      userId: user.id,
      sessionToken: token,
      deviceInfo: JSON.stringify(clientInfo),
      ipAddress: clientInfo.ipAddress,
      expiresAt: new Date(Date.now() + (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000))
    });

    // تسجيل محاولة ناجحة
    await LoginAttempt.create({
      ...loginAttempt,
      success: true,
      userId: user.id
    });

    // تحديث آخر تسجيل دخول
    await user.update({ lastLogin: new Date() });

    // إعداد بيانات المستخدم للإرسال
    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      phone: user.phone,
      profilePhoto: user.profilePhoto,
      role: user.role,
      departmentId: user.departmentId,
      departmentName: user.department?.name,
      employeeId: user.employeeId,
      position: user.position,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    logger.info(`تسجيل دخول ناجح للمستخدم: ${user.email}`, {
      userId: user.id,
      ipAddress: clientInfo.ipAddress
    });

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      data: {
        user: userData,
        token,
        expiresIn
      }
    });

  } catch (error) {
    logger.error('خطأ في تسجيل الدخول:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
};

/**
 * تسجيل الدخول باستخدام Google
 */
const signInWithGoogle = async (req, res) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({
        success: false,
        message: 'رمز Google مطلوب'
      });
    }

    // التحقق من صحة رمز Google
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    const { uid, email, name, picture } = decodedToken;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني مطلوب'
      });
    }

    const clientInfo = getClientInfo(req);

    // البحث عن المستخدم أو إنشاؤه
    let user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: ['department']
    });

    if (!user) {
      // إنشاء مستخدم جديد
      user = await User.create({
        email: email.toLowerCase(),
        name: name || '',
        profilePhoto: picture,
        role: 'employee', // الدور الافتراضي
        isActive: true,
        passwordHash: await bcrypt.hash(uid, 10) // استخدام uid كـ password hash
      });

      logger.info(`تم إنشاء مستخدم جديد من Google: ${email}`, {
        userId: user.id
      });
    }

    // التحقق من حالة المستخدم
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'تم إلغاء تفعيل حسابك'
      });
    }

    // إنشاء JWT token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    const token = generateToken(tokenPayload, '24h');

    // إنشاء جلسة جديدة
    await UserSession.create({
      userId: user.id,
      sessionToken: token,
      deviceInfo: JSON.stringify(clientInfo),
      ipAddress: clientInfo.ipAddress,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
    });

    // تسجيل محاولة ناجحة
    await LoginAttempt.create({
      userId: user.id,
      email: user.email,
      ipAddress: clientInfo.ipAddress,
      userAgent: clientInfo.userAgent,
      success: true
    });

    // تحديث آخر تسجيل دخول
    await user.update({ lastLogin: new Date() });

    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      phone: user.phone,
      profilePhoto: user.profilePhoto,
      role: user.role,
      departmentId: user.departmentId,
      departmentName: user.department?.name,
      employeeId: user.employeeId,
      position: user.position,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    logger.info(`تسجيل دخول ناجح بـ Google للمستخدم: ${user.email}`, {
      userId: user.id
    });

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      data: {
        user: userData,
        token,
        expiresIn: '24h'
      }
    });

  } catch (error) {
    logger.error('خطأ في تسجيل الدخول بـ Google:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
};

/**
 * تسجيل الخروج
 */
const signOut = async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (token) {
      // إلغاء تفعيل الجلسة
      await UserSession.destroy({
        where: { sessionToken: token }
      });
    }

    logger.info('تسجيل خروج المستخدم', {
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });

  } catch (error) {
    logger.error('خطأ في تسجيل الخروج:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
};

/**
 * إعادة تعيين كلمة المرور
 */
const resetPassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { email } = req.body;

    const user = await User.findOne({
      where: { email: email.toLowerCase() }
    });

    if (!user) {
      // لا نكشف عن عدم وجود المستخدم لأسباب أمنية
      return res.json({
        success: true,
        message: 'إذا كان البريد الإلكتروني موجود، فسيتم إرسال رابط إعادة التعيين'
      });
    }

    // إنشاء رمز إعادة التعيين
    const resetToken = generateToken({ userId: user.id }, '1h');
    
    await PasswordReset.create({
      userId: user.id,
      token: resetToken,
      expiresAt: new Date(Date.now() + 60 * 60 * 1000) // ساعة واحدة
    });

    // إرسال البريد الإلكتروني
    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    await sendEmail({
      to: user.email,
      subject: 'إعادة تعيين كلمة المرور - مراسِل',
      template: 'password-reset',
      data: {
        name: user.name,
        resetLink,
        expiresIn: '60 دقيقة'
      }
    });

    logger.info(`تم إرسال رابط إعادة تعيين كلمة المرور للمستخدم: ${user.email}`, {
      userId: user.id
    });

    res.json({
      success: true,
      message: 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني'
    });

  } catch (error) {
    logger.error('خطأ في إعادة تعيين كلمة المرور:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
};

/**
 * تأكيد إعادة تعيين كلمة المرور
 */
const confirmPasswordReset = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { token, newPassword } = req.body;

    // التحقق من صحة الرمز
    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(400).json({
        success: false,
        message: 'رمز غير صحيح أو منتهي الصلاحية'
      });
    }

    // البحث عن طلب إعادة التعيين
    const resetRequest = await PasswordReset.findOne({
      where: {
        token,
        usedAt: null,
        expiresAt: {
          [Op.gt]: new Date()
        }
      },
      include: ['user']
    });

    if (!resetRequest) {
      return res.status(400).json({
        success: false,
        message: 'رمز غير صحيح أو منتهي الصلاحية'
      });
    }

    // تحديث كلمة المرور
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    await resetRequest.user.update({
      passwordHash: hashedPassword
    });

    // تحديث طلب إعادة التعيين
    await resetRequest.update({
      usedAt: new Date()
    });

    // إلغاء جميع الجلسات النشطة للمستخدم
    await UserSession.destroy({
      where: { userId: resetRequest.userId }
    });

    logger.info(`تم تغيير كلمة المرور بنجاح للمستخدم: ${resetRequest.user.email}`, {
      userId: resetRequest.userId
    });

    res.json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    logger.error('خطأ في تأكيد إعادة تعيين كلمة المرور:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
};

/**
 * تغيير كلمة المرور
 */
const changePassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // التحقق من كلمة المرور الحالية
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور الحالية غير صحيحة'
      });
    }

    // تحديث كلمة المرور
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    await user.update({ passwordHash: hashedPassword });

    logger.info(`تم تغيير كلمة المرور للمستخدم: ${user.email}`, {
      userId: user.id
    });

    res.json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    logger.error('خطأ في تغيير كلمة المرور:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
};

/**
 * التحقق من صحة الرمز المميز
 */
const verifyTokenEndpoint = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      include: ['department']
    });

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'رمز غير صحيح'
      });
    }

    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      phone: user.phone,
      profilePhoto: user.profilePhoto,
      role: user.role,
      departmentId: user.departmentId,
      departmentName: user.department?.name,
      employeeId: user.employeeId,
      position: user.position,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.json({
      success: true,
      data: { user: userData }
    });

  } catch (error) {
    logger.error('خطأ في التحقق من الرمز:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الخادم'
    });
  }
};

module.exports = {
  signInWithEmailAndPassword,
  signInWithGoogle,
  signOut,
  resetPassword,
  confirmPasswordReset,
  changePassword,
  verifyTokenEndpoint
};
