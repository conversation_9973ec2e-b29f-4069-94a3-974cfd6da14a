# دليل إعداد البيئة التقنية - تطبيق مراسِل
## Development Environment Setup Guide - Morasel App

---

## 1. متطلبات النظام

### متطلبات الأجهزة
- **المعالج**: Intel i5 أو AMD Ryzen 5 أو أحدث
- **الذاكرة**: 8 GB RAM كحد أدنى (16 GB مفضل)
- **التخزين**: 50 GB مساحة فارغة على SSD
- **الشاشة**: دقة 1920x1080 أو أعلى

### أنظمة التشغيل المدعومة
- **Windows**: Windows 10/11 (64-bit)
- **macOS**: macOS 10.14 أو أحدث
- **Linux**: Ubuntu 18.04+ أو توزيعات مماثلة

---

## 2. تثبيت الأدوات الأساسية

### 2.1 Flutter SDK

#### Windows
```bash
# تحميل Flutter SDK
# من الموقع الرسمي: https://flutter.dev/docs/get-started/install/windows

# إضافة Flutter إلى PATH
# إضافة المسار التالي إلى متغيرات البيئة:
C:\flutter\bin

# التحقق من التثبيت
flutter doctor
```

#### macOS
```bash
# تثبيت باستخدام Homebrew
brew install flutter

# أو التحميل المباشر
# من: https://flutter.dev/docs/get-started/install/macos

# التحقق من التثبيت
flutter doctor
```

#### Linux
```bash
# تحميل Flutter
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.16.0-stable.tar.xz

# استخراج الملفات
tar xf flutter_linux_3.16.0-stable.tar.xz

# إضافة إلى PATH
export PATH="$PATH:`pwd`/flutter/bin"

# إضافة إلى .bashrc أو .zshrc
echo 'export PATH="$PATH:/path/to/flutter/bin"' >> ~/.bashrc

# التحقق من التثبيت
flutter doctor
```

### 2.2 Android Studio

#### التثبيت
1. تحميل من: https://developer.android.com/studio
2. تثبيت Android SDK
3. تثبيت Android SDK Command-line Tools
4. إعداد Android Virtual Device (AVD)

#### الإعدادات المطلوبة
```bash
# تثبيت Flutter plugin
# File > Settings > Plugins > Flutter

# إعداد Android SDK
# File > Settings > Appearance & Behavior > System Settings > Android SDK

# المكونات المطلوبة:
# - Android SDK Platform-Tools
# - Android SDK Build-Tools
# - Android 13 (API level 33)
# - Android 12 (API level 31)
# - Android 11 (API level 30)
```

### 2.3 Xcode (macOS فقط)

```bash
# تثبيت من App Store
# أو من: https://developer.apple.com/xcode/

# تثبيت Command Line Tools
xcode-select --install

# قبول الترخيص
sudo xcodebuild -license accept

# إعداد iOS Simulator
open -a Simulator
```

### 2.4 Visual Studio Code

#### التثبيت
1. تحميل من: https://code.visualstudio.com/
2. تثبيت الإضافات المطلوبة

#### الإضافات المطلوبة
```json
{
  "recommendations": [
    "dart-code.flutter",
    "dart-code.dart-code",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

---

## 3. إعداد مشروع Flutter

### 3.1 إنشاء المشروع

```bash
# إنشاء مشروع جديد
flutter create morasel_app

# الانتقال إلى مجلد المشروع
cd morasel_app

# تشغيل المشروع للتأكد من العمل
flutter run
```

### 3.2 هيكل المشروع

```
morasel_app/
├── android/                 # ملفات Android
├── ios/                     # ملفات iOS
├── lib/                     # الكود الرئيسي
│   ├── main.dart           # نقطة البداية
│   ├── core/               # الوظائف الأساسية
│   ├── features/           # الميزات
│   ├── shared/             # المكونات المشتركة
│   └── theme/              # الثيمات والألوان
├── assets/                 # الموارد (صور، خطوط)
├── test/                   # اختبارات الوحدة
├── integration_test/       # اختبارات التكامل
├── pubspec.yaml           # تبعيات المشروع
└── README.md              # وثائق المشروع
```

### 3.3 إعداد pubspec.yaml

```yaml
name: morasel_app
description: Corporate communication app for internal messaging
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  
  # UI Components
  material_design_icons_flutter: ^7.0.7296
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  
  # Navigation
  go_router: ^12.1.3
  
  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  
  # Authentication
  firebase_auth: ^4.15.3
  google_sign_in: ^6.1.6
  
  # Push Notifications
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^16.3.2
  
  # File Handling
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  path_provider: ^2.1.1
  
  # Real-time Communication
  socket_io_client: ^2.0.3+1
  
  # Video Calls
  agora_rtc_engine: ^6.3.0
  
  # Utils
  intl: ^0.19.0
  uuid: ^4.2.1
  url_launcher: ^6.2.2
  permission_handler: ^11.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  
  # Linting
  flutter_lints: ^3.0.1
  
  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
    
    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Light.ttf
          weight: 300
        - asset: assets/fonts/Tajawal-Regular.ttf
        - asset: assets/fonts/Tajawal-Medium.ttf
          weight: 500
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
```

---

## 4. إعداد Firebase

### 4.1 إنشاء مشروع Firebase

1. الذهاب إلى: https://console.firebase.google.com/
2. إنشاء مشروع جديد باسم "Morasel App"
3. تفعيل Google Analytics (اختياري)

### 4.2 إضافة التطبيقات

#### Android App
```bash
# إضافة Android app
# Package name: com.company.morasel_app
# تحميل google-services.json
# وضعه في: android/app/google-services.json
```

#### iOS App
```bash
# إضافة iOS app
# Bundle ID: com.company.moraselApp
# تحميل GoogleService-Info.plist
# وضعه في: ios/Runner/GoogleService-Info.plist
```

### 4.3 تفعيل الخدمات

```javascript
// في Firebase Console
// تفعيل الخدمات التالية:

// Authentication
// - Email/Password
// - Google Sign-In
// - Microsoft Sign-In

// Firestore Database
// - إنشاء قاعدة بيانات في production mode

// Cloud Storage
// - إنشاء bucket للملفات

// Cloud Messaging
// - تفعيل push notifications

// Cloud Functions
// - لمعالجة العمليات المعقدة
```

### 4.4 إعداد Firebase CLI

```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init

# اختيار الخدمات:
# - Firestore
# - Functions
# - Hosting
# - Storage
```

---

## 5. إعداد Backend (Node.js)

### 5.1 إنشاء مشروع Backend

```bash
# إنشاء مجلد المشروع
mkdir morasel_backend
cd morasel_backend

# تهيئة npm
npm init -y

# تثبيت التبعيات الأساسية
npm install express cors helmet morgan dotenv
npm install socket.io jsonwebtoken bcryptjs
npm install pg sequelize redis
npm install multer sharp ffmpeg-static

# تثبيت تبعيات التطوير
npm install -D nodemon @types/node typescript
npm install -D jest supertest @types/jest
```

### 5.2 هيكل Backend

```
morasel_backend/
├── src/
│   ├── controllers/        # تحكم في API
│   ├── models/            # نماذج قاعدة البيانات
│   ├── routes/            # مسارات API
│   ├── middleware/        # وسطاء
│   ├── services/          # خدمات العمل
│   ├── utils/             # أدوات مساعدة
│   ├── config/            # إعدادات
│   └── app.js             # تطبيق Express
├── tests/                 # اختبارات
├── uploads/               # ملفات مرفوعة
├── .env                   # متغيرات البيئة
├── package.json
└── server.js              # نقطة البداية
```

### 5.3 إعداد قاعدة البيانات

```bash
# تثبيت PostgreSQL
# Windows: تحميل من https://www.postgresql.org/download/windows/
# macOS: brew install postgresql
# Linux: sudo apt-get install postgresql

# إنشاء قاعدة البيانات
createdb morasel_db

# تشغيل سكريبت إنشاء الجداول
psql -d morasel_db -f database/schema.sql
```

---

## 6. أدوات التطوير الإضافية

### 6.1 Git و GitHub

```bash
# تهيئة Git
git init
git remote add origin https://github.com/company/morasel-app.git

# إنشاء .gitignore
echo "# Flutter
build/
.dart_tool/
.packages
.pub-cache/
.pub/

# IDE
.vscode/
.idea/

# Environment
.env
*.log" > .gitignore

# أول commit
git add .
git commit -m "Initial project setup"
git push -u origin main
```

### 6.2 Docker (اختياري)

```dockerfile
# Dockerfile للـ Backend
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    build: ./morasel_backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
    depends_on:
      - postgres
      - redis
  
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: morasel_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

---

## 7. اختبار البيئة

### 7.1 اختبار Flutter

```bash
# التحقق من إعداد Flutter
flutter doctor -v

# تشغيل التطبيق
flutter run

# تشغيل الاختبارات
flutter test
```

### 7.2 اختبار Backend

```bash
# تشغيل الخادم
npm run dev

# اختبار API
curl http://localhost:3000/api/health

# تشغيل الاختبارات
npm test
```

---

## 8. إعدادات الأمان

### 8.1 متغيرات البيئة

```bash
# .env للـ Backend
NODE_ENV=development
PORT=3000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=morasel_db
DB_USER=postgres
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your_super_secret_key
JWT_EXPIRES_IN=24h

# Redis
REDIS_URL=redis://localhost:6379

# Firebase
FIREBASE_PROJECT_ID=morasel-app
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email

# File Upload
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

### 8.2 إعدادات الأمان

```javascript
// في app.js
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// أمان الرؤوس
app.use(helmet());

// تحديد معدل الطلبات
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100 // حد أقصى 100 طلب لكل IP
});
app.use('/api/', limiter);

// CORS
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));
```

---

## 9. نصائح التطوير

### أفضل الممارسات
1. **استخدم Git بانتظام** - commit صغيرة ومتكررة
2. **اكتب الاختبارات** - قبل كتابة الكود
3. **اتبع معايير الكود** - استخدم linting
4. **وثق الكود** - تعليقات واضحة
5. **راجع الكود** - مراجعة الأقران

### أدوات مفيدة
- **Postman**: لاختبار API
- **DBeaver**: لإدارة قاعدة البيانات
- **Redis Desktop Manager**: لمراقبة Redis
- **Flutter Inspector**: لتصحيح UI

---

*هذا الدليل قابل للتحديث مع تطور المشروع*
