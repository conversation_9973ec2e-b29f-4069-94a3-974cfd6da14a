import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

/// نموذج المستخدم
@JsonSerializable()
class UserModel {
  final String id;
  final String email;
  final String name;
  final String? phone;
  final String? profilePhoto;
  final UserRole role;
  final String? departmentId;
  final String? departmentName;
  final String? employeeId;
  final String? position;
  final bool isActive;
  final DateTime? lastLogin;
  final DateTime createdAt;
  final DateTime updatedAt;
  final UserSettings? settings;

  const UserModel({
    required this.id,
    required this.email,
    required this.name,
    this.phone,
    this.profilePhoto,
    required this.role,
    this.departmentId,
    this.departmentName,
    this.employeeId,
    this.position,
    required this.isActive,
    this.lastLogin,
    required this.createdAt,
    required this.updatedAt,
    this.settings,
  });

  /// إنشاء من JSON
  factory UserModel.fromJson(String json) => 
      _$UserModelFromJson(jsonDecode(json));

  /// إنشاء من Map
  factory UserModel.fromMap(Map<String, dynamic> map) => 
      _$UserModelFromJson(map);

  /// تحويل إلى JSON
  String toJson() => jsonEncode(_$UserModelToJson(this));

  /// تحويل إلى Map
  Map<String, dynamic> toMap() => _$UserModelToJson(this);

  /// نسخ مع تعديل
  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? phone,
    String? profilePhoto,
    UserRole? role,
    String? departmentId,
    String? departmentName,
    String? employeeId,
    String? position,
    bool? isActive,
    DateTime? lastLogin,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserSettings? settings,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      profilePhoto: profilePhoto ?? this.profilePhoto,
      role: role ?? this.role,
      departmentId: departmentId ?? this.departmentId,
      departmentName: departmentName ?? this.departmentName,
      employeeId: employeeId ?? this.employeeId,
      position: position ?? this.position,
      isActive: isActive ?? this.isActive,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      settings: settings ?? this.settings,
    );
  }

  /// التحقق من الصلاحيات
  bool hasPermission(String permission) {
    switch (role) {
      case UserRole.admin:
        return true; // المدير له جميع الصلاحيات
      case UserRole.departmentManager:
        return _departmentManagerPermissions.contains(permission);
      case UserRole.administrator:
        return _administratorPermissions.contains(permission);
      case UserRole.employee:
        return _employeePermissions.contains(permission);
    }
  }

  /// التحقق من إمكانية إدارة المستخدم
  bool canManageUser(UserModel otherUser) {
    if (role == UserRole.admin) return true;
    if (role == UserRole.departmentManager && 
        departmentId == otherUser.departmentId) return true;
    return false;
  }

  /// الحصول على اسم الدور
  String get roleName {
    switch (role) {
      case UserRole.admin:
        return 'مدير النظام';
      case UserRole.departmentManager:
        return 'مدير القسم';
      case UserRole.administrator:
        return 'إداري';
      case UserRole.employee:
        return 'موظف';
    }
  }

  /// الحصول على لون الدور
  String get roleColor {
    switch (role) {
      case UserRole.admin:
        return '#F44336'; // أحمر
      case UserRole.departmentManager:
        return '#FF9800'; // برتقالي
      case UserRole.administrator:
        return '#2196F3'; // أزرق
      case UserRole.employee:
        return '#4CAF50'; // أخضر
    }
  }

  /// التحقق من كون المستخدم متصل
  bool get isOnline {
    if (lastLogin == null) return false;
    final now = DateTime.now();
    final difference = now.difference(lastLogin!);
    return difference.inMinutes < 5; // متصل إذا كان آخر نشاط خلال 5 دقائق
  }

  /// الحصول على حالة الاتصال
  UserStatus get status {
    if (!isActive) return UserStatus.offline;
    if (isOnline) return UserStatus.online;
    
    if (lastLogin == null) return UserStatus.offline;
    final now = DateTime.now();
    final difference = now.difference(lastLogin!);
    
    if (difference.inHours < 1) return UserStatus.away;
    return UserStatus.offline;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel{id: $id, name: $name, email: $email, role: $role}';
  }
}

/// أدوار المستخدمين
enum UserRole {
  @JsonValue('admin')
  admin,
  @JsonValue('department_manager')
  departmentManager,
  @JsonValue('administrator')
  administrator,
  @JsonValue('employee')
  employee,
}

/// حالات المستخدم
enum UserStatus {
  online,
  away,
  busy,
  offline,
}

/// إعدادات المستخدم
@JsonSerializable()
class UserSettings {
  final bool notificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String language;
  final String theme;
  final Map<String, bool> notificationTypes;

  const UserSettings({
    this.notificationsEnabled = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.language = 'ar',
    this.theme = 'system',
    this.notificationTypes = const {},
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingsToJson(this);

  UserSettings copyWith({
    bool? notificationsEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? language,
    String? theme,
    Map<String, bool>? notificationTypes,
  }) {
    return UserSettings(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      language: language ?? this.language,
      theme: theme ?? this.theme,
      notificationTypes: notificationTypes ?? this.notificationTypes,
    );
  }
}

/// صلاحيات مدير القسم
const List<String> _departmentManagerPermissions = [
  'user.view_department',
  'user.edit_department',
  'group.create',
  'group.edit',
  'group.delete',
  'meeting.create',
  'meeting.edit',
  'meeting.delete',
  'reports.view_department',
];

/// صلاحيات الإداري
const List<String> _administratorPermissions = [
  'group.create',
  'group.edit',
  'meeting.create',
  'meeting.edit',
  'task.create',
  'task.edit',
  'reports.view_limited',
];

/// صلاحيات الموظف
const List<String> _employeePermissions = [
  'message.send',
  'message.read',
  'file.upload',
  'file.download',
  'meeting.join',
  'profile.edit_own',
];

/// ملف تعريف مبسط للمستخدم
@JsonSerializable()
class UserProfile {
  final String id;
  final String name;
  final String? profilePhoto;
  final UserRole role;
  final String? position;
  final UserStatus status;

  const UserProfile({
    required this.id,
    required this.name,
    this.profilePhoto,
    required this.role,
    this.position,
    required this.status,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  factory UserProfile.fromUser(UserModel user) {
    return UserProfile(
      id: user.id,
      name: user.name,
      profilePhoto: user.profilePhoto,
      role: user.role,
      position: user.position,
      status: user.status,
    );
  }
}

/// معلومات تسجيل الدخول
@JsonSerializable()
class LoginInfo {
  final String email;
  final String password;
  final bool rememberMe;

  const LoginInfo({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  factory LoginInfo.fromJson(Map<String, dynamic> json) =>
      _$LoginInfoFromJson(json);

  Map<String, dynamic> toJson() => _$LoginInfoToJson(this);
}

/// طلب إعادة تعيين كلمة المرور
@JsonSerializable()
class PasswordResetRequest {
  final String email;

  const PasswordResetRequest({required this.email});

  factory PasswordResetRequest.fromJson(Map<String, dynamic> json) =>
      _$PasswordResetRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PasswordResetRequestToJson(this);
}

/// طلب تغيير كلمة المرور
@JsonSerializable()
class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;
  final String confirmPassword;

  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  factory ChangePasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$ChangePasswordRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ChangePasswordRequestToJson(this);

  /// التحقق من صحة البيانات
  bool get isValid {
    return currentPassword.isNotEmpty &&
           newPassword.length >= 6 &&
           newPassword == confirmPassword;
  }
}
