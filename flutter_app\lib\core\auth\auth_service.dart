import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../services/api_service.dart';
import '../utils/logger.dart';

/// خدمة المصادقة الرئيسية
/// تدير جميع عمليات تسجيل الدخول والخروج والتحقق
class AuthService {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final ApiService _apiService;
  final SharedPreferences _prefs;

  AuthService(this._apiService, this._prefs);

  /// المستخدم الحالي
  User? get currentUser => _firebaseAuth.currentUser;

  /// تدفق حالة المصادقة
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      Logger.info('محاولة تسجيل الدخول للمستخدم: $email');

      // التحقق من صحة البيانات
      if (!_isValidEmail(email)) {
        return AuthResult.failure('البريد الإلكتروني غير صحيح');
      }

      if (password.length < 6) {
        return AuthResult.failure('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      }

      // تسجيل الدخول في Firebase
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return AuthResult.failure('فشل في تسجيل الدخول');
      }

      // الحصول على معلومات المستخدم من الخادم
      final userModel = await _fetchUserProfile(credential.user!.uid);
      if (userModel == null) {
        return AuthResult.failure('لم يتم العثور على بيانات المستخدم');
      }

      // التحقق من حالة المستخدم
      if (!userModel.isActive) {
        await _firebaseAuth.signOut();
        return AuthResult.failure('تم إلغاء تفعيل حسابك. يرجى التواصل مع الإدارة');
      }

      // حفظ بيانات المستخدم محلياً
      await _saveUserLocally(userModel);

      // تحديث آخر تسجيل دخول
      await _updateLastLogin(userModel.id);

      Logger.info('تم تسجيل الدخول بنجاح للمستخدم: ${userModel.name}');
      return AuthResult.success(userModel);

    } on FirebaseAuthException catch (e) {
      Logger.error('خطأ Firebase Auth: ${e.code} - ${e.message}');
      return AuthResult.failure(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      Logger.error('خطأ في تسجيل الدخول: $e');
      return AuthResult.failure('حدث خطأ غير متوقع');
    }
  }

  /// تسجيل الدخول باستخدام Google
  Future<AuthResult> signInWithGoogle() async {
    try {
      Logger.info('محاولة تسجيل الدخول باستخدام Google');

      // بدء عملية تسجيل الدخول
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return AuthResult.failure('تم إلغاء تسجيل الدخول');
      }

      // الحصول على بيانات المصادقة
      final GoogleSignInAuthentication googleAuth = 
          await googleUser.authentication;

      // إنشاء credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // تسجيل الدخول في Firebase
      final userCredential = await _firebaseAuth.signInWithCredential(credential);
      
      if (userCredential.user == null) {
        return AuthResult.failure('فشل في تسجيل الدخول');
      }

      // التحقق من وجود المستخدم في النظام
      final userModel = await _fetchUserProfile(userCredential.user!.uid);
      if (userModel == null) {
        // إنشاء مستخدم جديد إذا لم يكن موجوداً
        final newUser = await _createUserFromGoogle(userCredential.user!);
        if (newUser == null) {
          await _firebaseAuth.signOut();
          return AuthResult.failure('غير مسموح بإنشاء حساب جديد');
        }
        await _saveUserLocally(newUser);
        return AuthResult.success(newUser);
      }

      // التحقق من حالة المستخدم
      if (!userModel.isActive) {
        await _firebaseAuth.signOut();
        return AuthResult.failure('تم إلغاء تفعيل حسابك');
      }

      await _saveUserLocally(userModel);
      await _updateLastLogin(userModel.id);

      Logger.info('تم تسجيل الدخول بنجاح باستخدام Google: ${userModel.name}');
      return AuthResult.success(userModel);

    } catch (e) {
      Logger.error('خطأ في تسجيل الدخول باستخدام Google: $e');
      return AuthResult.failure('فشل في تسجيل الدخول باستخدام Google');
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      Logger.info('تسجيل خروج المستخدم');

      // تسجيل الخروج من Firebase
      await _firebaseAuth.signOut();

      // تسجيل الخروج من Google
      await _googleSignIn.signOut();

      // مسح البيانات المحلية
      await _clearLocalData();

      Logger.info('تم تسجيل الخروج بنجاح');
    } catch (e) {
      Logger.error('خطأ في تسجيل الخروج: $e');
    }
  }

  /// إعادة تعيين كلمة المرور
  Future<AuthResult> resetPassword(String email) async {
    try {
      if (!_isValidEmail(email)) {
        return AuthResult.failure('البريد الإلكتروني غير صحيح');
      }

      await _firebaseAuth.sendPasswordResetEmail(email: email);
      
      Logger.info('تم إرسال رابط إعادة تعيين كلمة المرور إلى: $email');
      return AuthResult.success(null, 
          message: 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني');

    } on FirebaseAuthException catch (e) {
      Logger.error('خطأ في إعادة تعيين كلمة المرور: ${e.code}');
      return AuthResult.failure(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      Logger.error('خطأ في إعادة تعيين كلمة المرور: $e');
      return AuthResult.failure('حدث خطأ غير متوقع');
    }
  }

  /// تغيير كلمة المرور
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return AuthResult.failure('يجب تسجيل الدخول أولاً');
      }

      if (newPassword.length < 6) {
        return AuthResult.failure('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل');
      }

      // إعادة المصادقة
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );

      await user.reauthenticateWithCredential(credential);

      // تغيير كلمة المرور
      await user.updatePassword(newPassword);

      Logger.info('تم تغيير كلمة المرور بنجاح');
      return AuthResult.success(null, message: 'تم تغيير كلمة المرور بنجاح');

    } on FirebaseAuthException catch (e) {
      Logger.error('خطأ في تغيير كلمة المرور: ${e.code}');
      return AuthResult.failure(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      Logger.error('خطأ في تغيير كلمة المرور: $e');
      return AuthResult.failure('حدث خطأ غير متوقع');
    }
  }

  /// الحصول على المستخدم المحفوظ محلياً
  Future<UserModel?> getLocalUser() async {
    try {
      final userJson = _prefs.getString('current_user');
      if (userJson != null) {
        return UserModel.fromJson(userJson);
      }
    } catch (e) {
      Logger.error('خطأ في قراءة بيانات المستخدم المحلية: $e');
    }
    return null;
  }

  /// التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// الحصول على معلومات المستخدم من الخادم
  Future<UserModel?> _fetchUserProfile(String uid) async {
    try {
      final response = await _apiService.get('/users/profile/$uid');
      if (response.isSuccess && response.data != null) {
        return UserModel.fromMap(response.data);
      }
    } catch (e) {
      Logger.error('خطأ في جلب بيانات المستخدم: $e');
    }
    return null;
  }

  /// إنشاء مستخدم جديد من Google
  Future<UserModel?> _createUserFromGoogle(User firebaseUser) async {
    try {
      final userData = {
        'uid': firebaseUser.uid,
        'email': firebaseUser.email,
        'name': firebaseUser.displayName ?? '',
        'profilePhoto': firebaseUser.photoURL,
        'provider': 'google',
      };

      final response = await _apiService.post('/users/create-from-google', userData);
      if (response.isSuccess && response.data != null) {
        return UserModel.fromMap(response.data);
      }
    } catch (e) {
      Logger.error('خطأ في إنشاء مستخدم من Google: $e');
    }
    return null;
  }

  /// حفظ بيانات المستخدم محلياً
  Future<void> _saveUserLocally(UserModel user) async {
    try {
      await _prefs.setString('current_user', user.toJson());
      await _prefs.setString('user_id', user.id);
      await _prefs.setString('user_role', user.role.toString());
    } catch (e) {
      Logger.error('خطأ في حفظ بيانات المستخدم محلياً: $e');
    }
  }

  /// تحديث آخر تسجيل دخول
  Future<void> _updateLastLogin(String userId) async {
    try {
      await _apiService.patch('/users/$userId/last-login');
    } catch (e) {
      Logger.error('خطأ في تحديث آخر تسجيل دخول: $e');
    }
  }

  /// مسح البيانات المحلية
  Future<void> _clearLocalData() async {
    try {
      await _prefs.remove('current_user');
      await _prefs.remove('user_id');
      await _prefs.remove('user_role');
      await _prefs.remove('auth_token');
    } catch (e) {
      Logger.error('خطأ في مسح البيانات المحلية: $e');
    }
  }

  /// ترجمة رسائل خطأ Firebase
  String _getFirebaseErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى لاحقاً';
      case 'email-already-in-use':
        return 'هذا البريد الإلكتروني مستخدم بالفعل';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'network-request-failed':
        return 'فشل في الاتصال بالشبكة';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final UserModel? user;
  final String? message;
  final String? error;

  AuthResult._({
    required this.isSuccess,
    this.user,
    this.message,
    this.error,
  });

  factory AuthResult.success(UserModel? user, {String? message}) {
    return AuthResult._(
      isSuccess: true,
      user: user,
      message: message,
    );
  }

  factory AuthResult.failure(String error) {
    return AuthResult._(
      isSuccess: false,
      error: error,
    );
  }
}

/// مزود خدمة المصادقة
final authServiceProvider = Provider<AuthService>((ref) {
  final apiService = ref.read(apiServiceProvider);
  final prefs = ref.read(sharedPreferencesProvider);
  return AuthService(apiService, prefs);
});

/// مزود حالة المصادقة
final authStateProvider = StreamProvider<User?>((ref) {
  final authService = ref.read(authServiceProvider);
  return authService.authStateChanges;
});

/// مزود المستخدم الحالي
final currentUserProvider = FutureProvider<UserModel?>((ref) async {
  final authService = ref.read(authServiceProvider);
  return await authService.getLocalUser();
});
