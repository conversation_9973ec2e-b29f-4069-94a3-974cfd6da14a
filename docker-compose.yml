version: '3.8'

services:
  # Backend API Service
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: morasel-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=morasel_db
      - DB_USER=postgres
      - DB_PASSWORD=postgres123
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your_super_secret_jwt_key_here
      - JWT_EXPIRES_IN=24h
      - MAX_FILE_SIZE=50MB
      - UPLOAD_PATH=/app/uploads
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - morasel-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: morasel-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: morasel_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql
    ports:
      - "5432:5432"
    networks:
      - morasel-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d morasel_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache & Session Store
  redis:
    image: redis:7-alpine
    container_name: morasel-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    environment:
      - REDIS_PASSWORD=redis123
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - morasel-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: morasel-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - backend_uploads:/var/www/uploads
    depends_on:
      - backend
    networks:
      - morasel-network

  # MinIO Object Storage (Alternative to AWS S3)
  minio:
    image: minio/minio:latest
    container_name: morasel-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - morasel-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch for Search & Analytics
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: morasel-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - morasel-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana for Elasticsearch Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: morasel-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - morasel-network

  # Prometheus for Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: morasel-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - morasel-network

  # Grafana for Metrics Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: morasel-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - morasel-network

  # Mailhog for Email Testing (Development)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: morasel-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - morasel-network

# Named Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local

# Networks
networks:
  morasel-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
