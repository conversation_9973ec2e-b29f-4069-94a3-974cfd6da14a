# دليل إعداد Firebase - تطبيق مراسِل
## Firebase Setup Guide - Morasel App

---

## 1. إنشاء مشروع Firebase

### 1.1 إنشاء المشروع
1. الذهاب إلى [Firebase Console](https://console.firebase.google.com/)
2. النقر على "إنشاء مشروع" (Create a project)
3. إدخال اسم المشروع: `Morasel App`
4. تفعيل Google Analytics (اختياري)
5. اختيار حساب Analytics أو إنشاء حساب جديد
6. النقر على "إنشاء مشروع"

### 1.2 إعدادات المشروع الأساسية
```javascript
// معلومات المشروع
Project Name: Morasel App
Project ID: morasel-app-[unique-id]
Location: us-central1 (أو أقرب منطقة)
```

---

## 2. إضافة التطبيقات

### 2.1 إضافة تطبيق Android

#### الخطوات
1. في Firebase Console، النقر على أيقونة Android
2. إدخال معلومات التطبيق:
   ```
   Package name: com.company.morasel_app
   App nickname: Morasel Android
   Debug signing certificate SHA-1: [optional]
   ```
3. تحميل ملف `google-services.json`
4. وضع الملف في: `android/app/google-services.json`

#### إعداد Android
```gradle
// android/build.gradle (Project level)
buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.0'
    }
}

// android/app/build.gradle
apply plugin: 'com.google.gms.google-services'

dependencies {
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'
}
```

### 2.2 إضافة تطبيق iOS

#### الخطوات
1. في Firebase Console، النقر على أيقونة iOS
2. إدخال معلومات التطبيق:
   ```
   Bundle ID: com.company.moraselApp
   App nickname: Morasel iOS
   App Store ID: [optional]
   ```
3. تحميل ملف `GoogleService-Info.plist`
4. إضافة الملف إلى مشروع Xcode في مجلد `Runner`

#### إعداد iOS
```ruby
# ios/Podfile
platform :ios, '12.0'

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  
  # Firebase
  pod 'Firebase/Analytics'
  pod 'Firebase/Messaging'
end
```

### 2.3 إضافة تطبيق Web (اختياري)

```javascript
// Web app configuration
App nickname: Morasel Web
Hosting: Enable Firebase Hosting

// Firebase config object
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "morasel-app.firebaseapp.com",
  projectId: "morasel-app",
  storageBucket: "morasel-app.appspot.com",
  messagingSenderId: "*********",
  appId: "1:*********:web:abcdef123456"
};
```

---

## 3. تفعيل خدمات Firebase

### 3.1 Authentication

#### تفعيل الخدمة
1. الذهاب إلى Authentication > Get started
2. اختيار Sign-in method
3. تفعيل الطرق المطلوبة:

#### Email/Password
```javascript
// تفعيل Email/Password authentication
// في Firebase Console > Authentication > Sign-in method
// تفعيل Email/Password
// تفعيل Email link (passwordless sign-in) - اختياري
```

#### Google Sign-In
```javascript
// إعداد Google Sign-In
// 1. تفعيل Google في Sign-in method
// 2. إضافة Web SDK configuration
// 3. تحميل configuration files

// Android - إضافة SHA-1 fingerprint
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

// iOS - إعداد URL schemes في Info.plist
```

#### Microsoft Sign-In
```javascript
// إعداد Microsoft Azure AD
// 1. إنشاء Azure AD application
// 2. الحصول على Client ID
// 3. إعداد redirect URIs
// 4. تفعيل في Firebase Console
```

### 3.2 Firestore Database

#### إنشاء قاعدة البيانات
1. الذهاب إلى Firestore Database
2. النقر على "Create database"
3. اختيار "Start in production mode"
4. اختيار الموقع: `us-central1`

#### قواعد الأمان الأولية
```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Messages in conversations
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
      
      match /messages/{messageId} {
        allow read, write: if request.auth != null && 
          request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
      }
    }
    
    // Groups
    match /groups/{groupId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.uid in resource.data.admins;
    }
  }
}
```

#### فهارس مطلوبة
```javascript
// إنشاء فهارس في Firestore Console
// Collection: messages
// Fields: conversationId (Ascending), timestamp (Descending)

// Collection: notifications  
// Fields: userId (Ascending), isRead (Ascending), timestamp (Descending)

// Collection: conversations
// Fields: participants (Array), lastActivity (Descending)
```

### 3.3 Cloud Storage

#### إعداد التخزين
1. الذهاب إلى Storage
2. النقر على "Get started"
3. اختيار قواعد الأمان
4. اختيار الموقع: `us-central1`

#### قواعد الأمان
```javascript
// storage.rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User profile images
    match /users/{userId}/profile/{fileName} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.uid == userId &&
        resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    // Message attachments
    match /messages/{conversationId}/{messageId}/{fileName} {
      allow read, write: if request.auth != null;
      // Additional validation needed based on conversation membership
    }
    
    // Group images
    match /groups/{groupId}/{fileName} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      // Additional validation for group admins
    }
  }
}
```

### 3.4 Cloud Messaging (FCM)

#### إعداد Push Notifications
```javascript
// في Firebase Console > Cloud Messaging
// 1. لا حاجة لإعداد إضافي
// 2. سيتم استخدام Server Key في Backend

// Server Key (Legacy)
// نسخ من Project Settings > Cloud Messaging > Server key

// Service Account Key (Recommended)
// Project Settings > Service accounts > Generate new private key
```

#### إعداد Android
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<application>
    <!-- FCM Service -->
    <service
        android:name=".MyFirebaseMessagingService"
        android:exported="false">
        <intent-filter>
            <action android:name="com.google.firebase.MESSAGING_EVENT" />
        </intent-filter>
    </service>
    
    <!-- Default notification channel -->
    <meta-data
        android:name="com.google.firebase.messaging.default_notification_channel_id"
        android:value="high_importance_channel" />
</application>
```

#### إعداد iOS
```xml
<!-- ios/Runner/Info.plist -->
<key>UIBackgroundModes</key>
<array>
    <string>fetch</string>
    <string>remote-notification</string>
</array>
```

### 3.5 Cloud Functions

#### تثبيت Firebase CLI
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة Functions
firebase init functions

# اختيار JavaScript أو TypeScript
# اختيار ESLint
```

#### Functions أساسية
```javascript
// functions/index.js
const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// إرسال إشعار عند رسالة جديدة
exports.sendMessageNotification = functions.firestore
  .document('conversations/{conversationId}/messages/{messageId}')
  .onCreate(async (snap, context) => {
    const message = snap.data();
    const conversationId = context.params.conversationId;
    
    // الحصول على معلومات المحادثة
    const conversationDoc = await admin.firestore()
      .collection('conversations')
      .doc(conversationId)
      .get();
    
    const conversation = conversationDoc.data();
    const participants = conversation.participants;
    
    // إرسال إشعار لجميع المشاركين عدا المرسل
    const tokens = [];
    for (const participantId of participants) {
      if (participantId !== message.senderId) {
        const userDoc = await admin.firestore()
          .collection('users')
          .doc(participantId)
          .get();
        
        const userData = userDoc.data();
        if (userData.fcmToken) {
          tokens.push(userData.fcmToken);
        }
      }
    }
    
    if (tokens.length > 0) {
      const payload = {
        notification: {
          title: message.senderName,
          body: message.content,
          icon: 'default'
        },
        data: {
          conversationId: conversationId,
          messageId: context.params.messageId,
          type: 'message'
        }
      };
      
      await admin.messaging().sendToDevice(tokens, payload);
    }
  });

// إنشاء مستخدم جديد
exports.createUserProfile = functions.auth.user().onCreate(async (user) => {
  const userProfile = {
    uid: user.uid,
    email: user.email,
    displayName: user.displayName || '',
    photoURL: user.photoURL || '',
    createdAt: admin.firestore.FieldValue.serverTimestamp(),
    isActive: true,
    role: 'employee' // default role
  };
  
  await admin.firestore()
    .collection('users')
    .doc(user.uid)
    .set(userProfile);
});
```

---

## 4. إعداد Flutter مع Firebase

### 4.1 تثبيت الحزم
```yaml
# pubspec.yaml - تم إضافتها مسبقاً
dependencies:
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4
```

### 4.2 تهيئة Firebase
```dart
// lib/main.dart
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  runApp(MyApp());
}
```

### 4.3 إنشاء firebase_options.dart
```bash
# تشغيل FlutterFire CLI
dart pub global activate flutterfire_cli

# إنشاء ملف الإعدادات
flutterfire configure
```

---

## 5. اختبار الإعداد

### 5.1 اختبار Authentication
```dart
// اختبار تسجيل الدخول
try {
  final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
    email: '<EMAIL>',
    password: 'password123'
  );
  print('تم تسجيل الدخول بنجاح: ${credential.user?.uid}');
} catch (e) {
  print('خطأ في تسجيل الدخول: $e');
}
```

### 5.2 اختبار Firestore
```dart
// اختبار كتابة البيانات
await FirebaseFirestore.instance.collection('test').add({
  'message': 'Hello Firebase!',
  'timestamp': FieldValue.serverTimestamp(),
});

// اختبار قراءة البيانات
final snapshot = await FirebaseFirestore.instance.collection('test').get();
for (var doc in snapshot.docs) {
  print('${doc.id}: ${doc.data()}');
}
```

### 5.3 اختبار Cloud Messaging
```dart
// طلب إذن الإشعارات
NotificationSettings settings = await FirebaseMessaging.instance.requestPermission();

// الحصول على FCM token
String? token = await FirebaseMessaging.instance.getToken();
print('FCM Token: $token');

// الاستماع للرسائل
FirebaseMessaging.onMessage.listen((RemoteMessage message) {
  print('رسالة جديدة: ${message.notification?.title}');
});
```

---

## 6. نشر Functions

```bash
# نشر جميع Functions
firebase deploy --only functions

# نشر function محددة
firebase deploy --only functions:sendMessageNotification

# عرض logs
firebase functions:log
```

---

## 7. مراقبة الأداء

### 7.1 Firebase Performance
```dart
// إضافة مراقبة الأداء
dependencies:
  firebase_performance: ^0.9.3+6

// في الكود
final trace = FirebasePerformance.instance.newTrace('message_send');
await trace.start();
// ... عملية إرسال الرسالة
await trace.stop();
```

### 7.2 Firebase Crashlytics
```dart
// إضافة تتبع الأخطاء
dependencies:
  firebase_crashlytics: ^3.4.8

// في main.dart
FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

// تسجيل خطأ مخصص
FirebaseCrashlytics.instance.recordError(
  exception,
  stackTrace,
  reason: 'سبب الخطأ'
);
```

---

*هذا الدليل يغطي الإعداد الأساسي لـ Firebase. قد تحتاج إلى تخصيصات إضافية حسب احتياجات المشروع.*
