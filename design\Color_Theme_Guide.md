# دليل الألوان والثيمات - تطبيق مراسِل
## Color & Theme Guide - Morasel App

---

## 1. نظام الألوان الأساسي

### الألوان الرئيسية (Primary Colors)
```css
/* الأزرق الأساسي */
--primary-50: #E3F2FD
--primary-100: #BBDEFB
--primary-200: #90CAF9
--primary-300: #64B5F6
--primary-400: #42A5F5
--primary-500: #2196F3  /* اللون الأساسي */
--primary-600: #1E88E5
--primary-700: #1976D2
--primary-800: #1565C0
--primary-900: #0D47A1
```

### الألوان الثانوية (Secondary Colors)
```css
/* الأخضر الثانوي */
--secondary-50: #E8F5E8
--secondary-100: #C8E6C9
--secondary-200: #A5D6A7
--secondary-300: #81C784
--secondary-400: #66BB6A
--secondary-500: #4CAF50  /* اللون الثانوي */
--secondary-600: #43A047
--secondary-700: #388E3C
--secondary-800: #2E7D32
--secondary-900: #1B5E20
```

### ألوان الحالة (Status Colors)
```css
/* النجاح */
--success-light: #81C784
--success-main: #4CAF50
--success-dark: #388E3C

/* التحذير */
--warning-light: #FFB74D
--warning-main: #FF9800
--warning-dark: #F57C00

/* الخطأ */
--error-light: #E57373
--error-main: #F44336
--error-dark: #D32F2F

/* المعلومات */
--info-light: #64B5F6
--info-main: #2196F3
--info-dark: #1976D2
```

---

## 2. الوضع الفاتح (Light Mode)

### ألوان الخلفية
```css
--background-default: #FFFFFF
--background-paper: #FFFFFF
--background-level1: #F5F5F5
--background-level2: #EEEEEE
--background-level3: #E0E0E0
```

### ألوان النص
```css
--text-primary: #212121
--text-secondary: #757575
--text-disabled: #BDBDBD
--text-hint: #9E9E9E
```

### ألوان الحدود والفواصل
```css
--divider: #E0E0E0
--border-light: #F0F0F0
--border-main: #E0E0E0
--border-dark: #BDBDBD
```

### ألوان الأسطح
```css
--surface-main: #FFFFFF
--surface-variant: #F5F5F5
--surface-container: #FAFAFA
--surface-container-high: #F0F0F0
```

---

## 3. الوضع الليلي (Dark Mode)

### ألوان الخلفية
```css
--background-default: #121212
--background-paper: #1E1E1E
--background-level1: #242424
--background-level2: #2A2A2A
--background-level3: #303030
```

### ألوان النص
```css
--text-primary: #FFFFFF
--text-secondary: #B0B0B0
--text-disabled: #6C6C6C
--text-hint: #808080
```

### ألوان الحدود والفواصل
```css
--divider: #373737
--border-light: #2A2A2A
--border-main: #373737
--border-dark: #4A4A4A
```

### ألوان الأسطح
```css
--surface-main: #1E1E1E
--surface-variant: #242424
--surface-container: #2A2A2A
--surface-container-high: #303030
```

---

## 4. ألوان المراسلة

### فقاعات الرسائل
```css
/* الرسائل المرسلة */
--message-sent-bg: var(--primary-500)
--message-sent-text: #FFFFFF

/* الرسائل المستقبلة - الوضع الفاتح */
--message-received-bg-light: #F5F5F5
--message-received-text-light: var(--text-primary)

/* الرسائل المستقبلة - الوضع الليلي */
--message-received-bg-dark: #2A2A2A
--message-received-text-dark: var(--text-primary)
```

### حالات الرسائل
```css
--message-sending: #FFB74D
--message-sent: #4CAF50
--message-delivered: #4CAF50
--message-read: #2196F3
--message-failed: #F44336
```

### الإشارات والتفاعلات
```css
--mention-bg: #FFF3E0
--mention-text: #E65100
--mention-border: #FFB74D

--reaction-bg: #E3F2FD
--reaction-text: var(--primary-700)
--reaction-border: var(--primary-300)
```

---

## 5. ألوان الحالة والمؤشرات

### حالة الاتصال
```css
--status-online: #4CAF50
--status-away: #FF9800
--status-busy: #F44336
--status-offline: #9E9E9E
```

### مؤشرات العد
```css
--badge-bg: #F44336
--badge-text: #FFFFFF
--badge-border: #FFFFFF

--unread-count-bg: var(--primary-500)
--unread-count-text: #FFFFFF
```

### أولوية المهام
```css
--priority-low: #4CAF50
--priority-medium: #FF9800
--priority-high: #F44336
--priority-urgent: #9C27B0
```

---

## 6. ألوان الاجتماعات والبث

### حالة الاجتماعات
```css
--meeting-scheduled: #2196F3
--meeting-live: #F44336
--meeting-ended: #9E9E9E
--meeting-cancelled: #FF5722
```

### البث المباشر
```css
--stream-live: #F44336
--stream-scheduled: #FF9800
--stream-ended: #9E9E9E
--stream-viewers: #4CAF50
```

---

## 7. ثيمات المؤسسات

### الثيم الحكومي
```css
--gov-primary: #1B5E20
--gov-secondary: #2E7D32
--gov-accent: #FFD700
```

### الثيم المصرفي
```css
--bank-primary: #0D47A1
--bank-secondary: #1565C0
--bank-accent: #FFC107
```

### الثيم التقني
```css
--tech-primary: #6A1B9A
--tech-secondary: #8E24AA
--tech-accent: #00E676
```

### الثيم الطبي
```css
--medical-primary: #C62828
--medical-secondary: #D32F2F
--medical-accent: #4CAF50
```

---

## 8. تدرجات الألوان (Gradients)

### التدرجات الأساسية
```css
--gradient-primary: linear-gradient(135deg, #2196F3 0%, #1976D2 100%)
--gradient-secondary: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%)
--gradient-success: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)
--gradient-warning: linear-gradient(135deg, #FF9800 0%, #F57C00 100%)
--gradient-error: linear-gradient(135deg, #F44336 0%, #D32F2F 100%)
```

### تدرجات الخلفية
```css
--gradient-bg-light: linear-gradient(180deg, #FAFAFA 0%, #F5F5F5 100%)
--gradient-bg-dark: linear-gradient(180deg, #1E1E1E 0%, #121212 100%)
```

---

## 9. الشفافية والظلال

### مستويات الشفافية
```css
--opacity-disabled: 0.38
--opacity-secondary: 0.6
--opacity-medium: 0.8
--opacity-overlay: 0.5
```

### الظلال (Elevation)
```css
--shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)
--shadow-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)
--shadow-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)
--shadow-4: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22)
--shadow-5: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22)
```

---

## 10. تطبيق الألوان في Flutter

### ملف الثيم الأساسي
```dart
// lib/theme/app_colors.dart
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryVariant = Color(0xFF1976D2);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF4CAF50);
  static const Color secondaryVariant = Color(0xFF388E3C);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFF FF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Light Theme
  static const Color backgroundLight = Color(0xFFFFFFFF);
  static const Color surfaceLight = Color(0xFFF5F5F5);
  static const Color textPrimaryLight = Color(0xFF212121);
  
  // Dark Theme
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  static const Color textPrimaryDark = Color(0xFFFFFFFF);
}
```

### تعريف الثيمات
```dart
// lib/theme/app_theme.dart
class AppTheme {
  static ThemeData lightTheme = ThemeData(
    primarySwatch: MaterialColor(0xFF2196F3, {
      50: Color(0xFFE3F2FD),
      100: Color(0xFFBBDEFB),
      200: Color(0xFF90CAF9),
      300: Color(0xFF64B5F6),
      400: Color(0xFF42A5F5),
      500: Color(0xFF2196F3),
      600: Color(0xFF1E88E5),
      700: Color(0xFF1976D2),
      800: Color(0xFF1565C0),
      900: Color(0xFF0D47A1),
    }),
    brightness: Brightness.light,
    backgroundColor: AppColors.backgroundLight,
    // ... باقي إعدادات الثيم
  );
  
  static ThemeData darkTheme = ThemeData(
    primarySwatch: MaterialColor(0xFF2196F3, {
      // نفس التدرجات
    }),
    brightness: Brightness.dark,
    backgroundColor: AppColors.backgroundDark,
    // ... باقي إعدادات الثيم
  );
}
```

---

## 11. إرشادات الاستخدام

### قواعد الألوان
1. **التباين**: تأكد من تباين كافي بين النص والخلفية
2. **الاتساق**: استخدم نفس الألوان للعناصر المتشابهة
3. **إمكانية الوصول**: اتبع معايير WCAG للألوان
4. **الثقافة**: راعي المعاني الثقافية للألوان

### أفضل الممارسات
- استخدم الألوان الأساسية للعناصر المهمة
- استخدم الألوان الثانوية للعناصر الداعمة
- استخدم ألوان الحالة للتغذية الراجعة
- تجنب استخدام الأحمر والأخضر معاً للمستخدمين المصابين بعمى الألوان

---

*هذا الدليل قابل للتحديث والتخصيص حسب احتياجات المؤسسة*
