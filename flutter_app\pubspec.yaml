name: morasel_app
description: تطبيق مراسِل - منصة التواصل الداخلي للمؤسسات
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3

  # UI Components & Icons
  material_design_icons_flutter: ^7.0.7296
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0
  lottie: ^2.7.0

  # Navigation
  go_router: ^12.1.3
  auto_route: ^7.9.2

  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  pretty_dio_logger: ^1.3.1

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0

  # Authentication & Security
  firebase_auth: ^4.15.3
  google_sign_in: ^6.1.6
  local_auth: ^2.1.7
  crypto: ^3.0.3

  # Push Notifications
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^16.3.2
  firebase_core: ^2.24.2

  # File Handling
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  path_provider: ^2.1.1
  open_file: ^3.3.2
  mime: ^1.0.4

  # Real-time Communication
  socket_io_client: ^2.0.3+1
  web_socket_channel: ^2.4.0

  # Video Calls & Streaming
  agora_rtc_engine: ^6.3.0
  camera: ^0.10.5+5
  video_player: ^2.8.1

  # Audio
  just_audio: ^0.9.36
  audio_waveforms: ^1.0.5
  record: ^5.0.4

  # Utils & Helpers
  intl: ^0.19.0
  uuid: ^4.2.1
  url_launcher: ^6.2.2
  permission_handler: ^11.1.0
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  connectivity_plus: ^5.0.2
  
  # Date & Time
  timeago: ^3.6.0
  flutter_datetime_picker_plus: ^2.1.0

  # Image Processing
  image: ^4.1.3
  flutter_image_compress: ^2.1.0

  # QR Code
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1

  # Animations
  flutter_animate: ^4.3.0
  rive: ^0.12.4

  # Charts & Analytics
  fl_chart: ^0.65.0

  # Theming
  flex_color_scheme: ^7.3.1
  dynamic_color: ^1.6.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  riverpod_generator: ^2.3.9
  auto_route_generator: ^7.3.2

  # Linting & Analysis
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0

  # Testing
  mockito: ^5.4.4
  mocktail: ^1.0.2
  network_image_mock: ^2.1.1

  # Launcher Icons
  flutter_launcher_icons: ^0.13.1

  # Native Splash
  flutter_native_splash: ^2.3.8

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/sounds/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-ExtraBold.ttf
          weight: 800

    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Tajawal-Light.ttf
          weight: 300
        - asset: assets/fonts/Tajawal-Regular.ttf
          weight: 400
        - asset: assets/fonts/Tajawal-Medium.ttf
          weight: 500
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
        - asset: assets/fonts/Tajawal-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Tajawal-Black.ttf
          weight: 900

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#2196F3"
    theme_color: "#2196F3"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icons/app_icon.png"

# Native Splash Screen Configuration
flutter_native_splash:
  color: "#2196F3"
  image: assets/images/splash_logo.png
  color_dark: "#1976D2"
  image_dark: assets/images/splash_logo_dark.png
  android_12:
    image: assets/images/splash_logo_android12.png
    icon_background_color: "#2196F3"
    image_dark: assets/images/splash_logo_android12_dark.png
    icon_background_color_dark: "#1976D2"
  web: false
