# وثيقة متطلبات النظام (SRS) - تطبيق مراسِل
## System Requirements Specification - Morasel App

### معلومات المشروع
- **اسم التطبيق**: مراسِل (Morasel)
- **النوع**: تطبيق تواصل داخلي للمؤسسات
- **المنصات**: iOS & Android
- **التاريخ**: 2025-07-12
- **الإصدار**: 1.0

---

## 1. نظرة عامة على المشروع

### 1.1 الهدف
تطوير تطبيق جوال مغلق للتواصل الداخلي في المؤسسات، يوفر منصة آمنة وشاملة للموظفين والإدارة للتواصل والتعاون.

### 1.2 النطاق
- تطبيق جوال متعدد المنصات (iOS/Android)
- واجهة عربية كاملة (RTL)
- نظام صلاحيات متدرج
- تشفير شامل للبيانات
- تكامل مع أنظمة المؤسسة

### 1.3 المستخدمون المستهدفون
- **المدراء**: صلاحيات شاملة
- **مشرفو الأقسام**: إشراف على الأقسام
- **الإداريون**: تنفيذ المهام
- **الموظفون**: تفاعل محدود

---

## 2. المتطلبات الوظيفية

### 2.1 نظام المصادقة والأمان
#### 2.1.1 تسجيل الدخول
- تسجيل دخول بالبريد المؤسسي وكلمة المرور
- دعم تسجيل الدخول الموحد (SSO)
  - Google SSO
  - Microsoft Azure AD
- التحقق بخطوتين (2FA) اختياري
- حماية من محاولات الدخول المتكررة

#### 2.1.2 إدارة الحسابات
- إنشاء المستخدمين من قبل الإدارة فقط
- إرسال رابط تفعيل للمستخدمين الجدد
- استعادة كلمة المرور عبر البريد
- تغيير كلمة المرور من الملف الشخصي
- تشفير آمن لكلمات المرور

### 2.2 نظام المراسلة الفورية
#### 2.2.1 أنواع المحادثات
- محادثات فردية
- مجموعات دردشة حسب:
  - الفرق
  - الأقسام
  - المشاريع

#### 2.2.2 أنواع الرسائل
- رسائل نصية
- صور
- ملفات
- روابط
- رسائل صوتية
- جدولة الرسائل

#### 2.2.3 ميزات متقدمة
- Mentions داخل المحادثات
- نظام المهام والتنبيهات
- مكالمات فيديو
- مشاركة الشاشة

### 2.3 نظام الاجتماعات
#### 2.3.1 إنشاء الاجتماعات
- صفحة إنشاء اجتماع
- اختيار المشاركين
- تحديد التوقيت
- الاجتماعات العامة (للمدير فقط)

#### 2.3.2 إدارة الاجتماعات
- دعوات تلقائية
- تذكيرات
- تسجيل الحضور
- مشاركة الملفات

### 2.4 نظام البث المباشر
#### 2.4.1 البث
- صفحة بث مباشر
- دعم الرسائل أثناء البث
- تحكم في الوصول
- تسجيل البث (اختياري)

#### 2.4.2 التفاعل
- رسائل مباشرة
- ردود فعل
- أسئلة وأجوبة

### 2.5 نظام الإشعارات
#### 2.5.1 أنواع الإشعارات
- إشعارات الرسائل
- إشعارات المهام
- إشعارات الاجتماعات
- إشعارات البث المباشر
- Mentions

#### 2.5.2 إدارة الإشعارات
- صفحة إشعارات قابلة للتخصيص
- فلترة الإشعارات
- إعدادات التنبيهات
- إشعارات push

### 2.6 إدارة الملفات والوسائط
#### 2.6.1 تنظيم الملفات
- صفحة مستقلة للملفات
- تصنيف الملفات
- فلترة متقدمة
- بحث في الملفات

#### 2.6.2 أنواع الملفات المدعومة
- المستندات (PDF, DOC, XLS, PPT)
- الصور (JPG, PNG, GIF)
- الفيديو (MP4, AVI, MOV)
- الصوت (MP3, WAV, AAC)

### 2.7 الملف الشخصي
- معلومات شخصية قابلة للتعديل
- صورة الملف الشخصي
- معلومات الاتصال
- إعدادات الخصوصية

---

## 3. نظام الصلاحيات

### 3.1 الأدوار الأساسية

#### 3.1.1 المدير (Admin)
- صلاحيات شاملة
- إدارة جميع المستخدمين
- إنشاء الاجتماعات العامة
- الوصول لجميع البيانات
- إدارة النسخ الاحتياطية

#### 3.1.2 مشرف القسم (Department Manager)
- إشراف على قسم محدد
- إدارة موظفي القسم
- إنشاء مجموعات القسم
- عرض تقارير القسم

#### 3.1.3 الإداري (Administrator)
- تنفيذ مهام ضمن فرق العمل
- إدارة المشاريع المخصصة
- إنشاء مجموعات المشاريع

#### 3.1.4 الموظف (Employee)
- تفاعل محدود
- المشاركة في المحادثات المخصصة
- عرض الملف الشخصي فقط

### 3.2 الصلاحيات القابلة للتخصيص
- نظام صلاحيات مرن
- تخصيص الصلاحيات من لوحة التحكم
- صلاحيات على مستوى الميزة
- صلاحيات على مستوى البيانات

---

## 4. لوحة التحكم الإدارية

### 4.1 إدارة المستخدمين
#### 4.1.1 إضافة المستخدمين
- إضافة يدوية
- استيراد من ملفات CSV
- ربط Active Directory
- ربط LDAP

#### 4.1.2 إدارة الصلاحيات
- تعيين الأدوار
- تعديل الصلاحيات
- إلغاء تفعيل المستخدمين
- إعادة تعيين كلمات المرور

### 4.2 سجل النشاطات
- تسجيل جميع الأنشطة
- فلترة حسب المستخدم
- فلترة حسب النشاط
- تصدير التقارير

### 4.3 التحليلات والتقارير
- إحصائيات الاستخدام
- تقارير النشاط
- تحليل الأداء
- تقارير الأمان

---

## 5. النسخ الاحتياطي

### 5.1 النسخ التلقائي
- نسخ دوري (يومي/أسبوعي)
- نسخ المحادثات
- نسخ الملفات
- نسخ إعدادات المستخدمين

### 5.2 الاسترجاع
- استرجاع انتقائي
- استرجاع كامل
- استرجاع حسب التاريخ
- تشفير النسخ الاحتياطية

---

## 6. المتطلبات غير الوظيفية

### 6.1 الأداء
- زمن استجابة أقل من 2 ثانية
- دعم 1000+ مستخدم متزامن
- تحميل الرسائل بشكل تدريجي
- ضغط الملفات الكبيرة

### 6.2 الأمان
- تشفير end-to-end للرسائل
- تشفير البيانات في قاعدة البيانات
- مصادقة متعددة العوامل
- حماية من هجمات CSRF/XSS

### 6.3 قابلية الاستخدام
- واجهة عربية كاملة (RTL)
- دعم الوضع الليلي
- تصميم تفاعلي (Responsive)
- إمكانية الوصول (Accessibility)

### 6.4 التوافق
- iOS 12.0+
- Android 8.0+ (API 26+)
- دعم الأجهزة اللوحية
- دعم الاتجاهات المختلفة

### 6.5 الموثوقية
- توفر 99.9%
- استرجاع تلقائي من الأخطاء
- نسخ احتياطي تلقائي
- مراقبة النظام

---

## 7. التقنيات المقترحة

### 7.1 تطوير التطبيق
- **Flutter**: للتطوير متعدد المنصات
- **Dart**: لغة البرمجة
- **Provider/Riverpod**: إدارة الحالة
- **Hive/SQLite**: قاعدة البيانات المحلية

### 7.2 الخادم والبنية التحتية
- **Node.js + Express**: خادم التطبيق
- **PostgreSQL**: قاعدة البيانات الرئيسية
- **Redis**: التخزين المؤقت
- **Socket.io**: الاتصال الفوري

### 7.3 الخدمات السحابية
- **Firebase**: الإشعارات والمصادقة
- **AWS S3/Google Cloud**: تخزين الملفات
- **WebRTC**: مكالمات الفيديو
- **Docker**: نشر التطبيق

### 7.4 الأمان
- **JWT**: رموز المصادقة
- **bcrypt**: تشفير كلمات المرور
- **SSL/TLS**: تشفير الاتصال
- **OAuth 2.0**: تسجيل الدخول الموحد

---

## 8. خطة التطوير

### المرحلة 1: الإعداد والتصميم (أسبوعان)
- إعداد البيئة التقنية
- تصميم قاعدة البيانات
- تصميم واجهات المستخدم

### المرحلة 2: الأساسيات (4 أسابيع)
- نظام المصادقة
- المراسلة الأساسية
- الملفات الشخصية

### المرحلة 3: الميزات المتقدمة (6 أسابيع)
- الاجتماعات
- البث المباشر
- الإشعارات المتقدمة

### المرحلة 4: الإدارة والأمان (4 أسابيع)
- لوحة التحكم
- النسخ الاحتياطي
- تحسينات الأمان

### المرحلة 5: الاختبار والنشر (2 أسبوع)
- اختبار شامل
- إصلاح الأخطاء
- النشر على المتاجر

---

## 9. معايير القبول

### 9.1 الوظائف الأساسية
- ✅ تسجيل دخول آمن
- ✅ مراسلة فورية
- ✅ مجموعات دردشة
- ✅ مشاركة الملفات

### 9.2 الأمان
- ✅ تشفير البيانات
- ✅ مصادقة متعددة العوامل
- ✅ حماية من الهجمات

### 9.3 الأداء
- ✅ زمن استجابة < 2 ثانية
- ✅ دعم 1000+ مستخدم
- ✅ استقرار 99.9%

---

## 10. المخاطر والتحديات

### 10.1 المخاطر التقنية
- تعقيد تكامل WebRTC
- أداء قاعدة البيانات مع البيانات الكبيرة
- تحديات الأمان

### 10.2 المخاطر التشغيلية
- تدريب المستخدمين
- مقاومة التغيير
- متطلبات الامتثال

### 10.3 خطط التخفيف
- اختبار مكثف للميزات المعقدة
- تحسين قاعدة البيانات
- تدريب شامل للمستخدمين

---

*هذه الوثيقة قابلة للتحديث حسب متطلبات المشروع*
