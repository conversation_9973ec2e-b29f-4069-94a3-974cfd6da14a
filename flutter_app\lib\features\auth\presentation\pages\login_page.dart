import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_overlay.dart';
import '../../../../core/utils/validators.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../controllers/auth_controller.dart';
import '../widgets/social_login_buttons.dart';
import '../widgets/auth_header.dart';

/// صفحة تسجيل الدخول
class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _obscurePassword = true;
  bool _rememberMe = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 40),
                  
                  // رأس الصفحة مع الشعار
                  const AuthHeader(
                    title: 'مرحباً بك',
                    subtitle: 'سجل دخولك للمتابعة',
                  ),
                  
                  const SizedBox(height: 48),
                  
                  // حقل البريد الإلكتروني
                  CustomTextField(
                    controller: _emailController,
                    label: 'البريد الإلكتروني',
                    hint: 'أدخل بريدك الإلكتروني',
                    prefixIcon: MdiIcons.email,
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    validator: Validators.email,
                    onChanged: (_) => _clearErrors(),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // حقل كلمة المرور
                  CustomTextField(
                    controller: _passwordController,
                    label: 'كلمة المرور',
                    hint: 'أدخل كلمة المرور',
                    prefixIcon: MdiIcons.lock,
                    obscureText: _obscurePassword,
                    textInputAction: TextInputAction.done,
                    validator: Validators.password,
                    onChanged: (_) => _clearErrors(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword ? MdiIcons.eyeOff : MdiIcons.eye,
                        color: AppColors.textSecondary,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    onSubmitted: (_) => _handleLogin(),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // تذكرني ونسيت كلمة المرور
                  Row(
                    children: [
                      Checkbox(
                        value: _rememberMe,
                        onChanged: (value) {
                          setState(() {
                            _rememberMe = value ?? false;
                          });
                        },
                        activeColor: AppColors.primary,
                      ),
                      Text(
                        'تذكرني',
                        style: AppTextStyles.bodyMedium,
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: _handleForgotPassword,
                        child: Text(
                          'نسيت كلمة المرور؟',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // زر تسجيل الدخول
                  CustomButton(
                    text: 'تسجيل الدخول',
                    onPressed: _handleLogin,
                    isLoading: _isLoading,
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // فاصل "أو"
                  Row(
                    children: [
                      const Expanded(child: Divider()),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          'أو',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                      const Expanded(child: Divider()),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // أزرار تسجيل الدخول الاجتماعي
                  SocialLoginButtons(
                    onGooglePressed: _handleGoogleLogin,
                    onMicrosoftPressed: _handleMicrosoftLogin,
                    isLoading: _isLoading,
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // رابط المساعدة
                  Center(
                    child: TextButton.icon(
                      onPressed: _handleHelp,
                      icon: Icon(
                        MdiIcons.helpCircle,
                        color: AppColors.textSecondary,
                        size: 20,
                      ),
                      label: Text(
                        'تحتاج مساعدة؟',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// معالج تسجيل الدخول
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final authController = ref.read(authControllerProvider.notifier);
      
      final result = await authController.signInWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        rememberMe: _rememberMe,
      );

      if (result.isSuccess) {
        if (mounted) {
          SnackBarUtils.showSuccess(
            context,
            'تم تسجيل الدخول بنجاح',
          );
          context.go('/home');
        }
      } else {
        if (mounted) {
          SnackBarUtils.showError(
            context,
            result.error ?? 'فشل في تسجيل الدخول',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showError(
          context,
          'حدث خطأ غير متوقع',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// معالج تسجيل الدخول بـ Google
  Future<void> _handleGoogleLogin() async {
    setState(() => _isLoading = true);

    try {
      final authController = ref.read(authControllerProvider.notifier);
      
      final result = await authController.signInWithGoogle();

      if (result.isSuccess) {
        if (mounted) {
          SnackBarUtils.showSuccess(
            context,
            'تم تسجيل الدخول بنجاح',
          );
          context.go('/home');
        }
      } else {
        if (mounted) {
          SnackBarUtils.showError(
            context,
            result.error ?? 'فشل في تسجيل الدخول',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showError(
          context,
          'حدث خطأ غير متوقع',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// معالج تسجيل الدخول بـ Microsoft
  Future<void> _handleMicrosoftLogin() async {
    // TODO: تنفيذ تسجيل الدخول بـ Microsoft
    SnackBarUtils.showInfo(
      context,
      'تسجيل الدخول بـ Microsoft قريباً',
    );
  }

  /// معالج نسيت كلمة المرور
  void _handleForgotPassword() {
    context.push('/forgot-password');
  }

  /// معالج المساعدة
  void _handleHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('المساعدة'),
        content: const Text(
          'للحصول على المساعدة في تسجيل الدخول، يرجى التواصل مع قسم تقنية المعلومات في مؤسستك.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  /// مسح الأخطاء عند تغيير النص
  void _clearErrors() {
    // يمكن إضافة منطق لمسح الأخطاء هنا
  }
}

/// صفحة نسيت كلمة المرور
class ForgotPasswordPage extends ConsumerStatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  ConsumerState<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends ConsumerState<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('إعادة تعيين كلمة المرور'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 32),
                
                Icon(
                  MdiIcons.lockReset,
                  size: 80,
                  color: AppColors.primary,
                ),
                
                const SizedBox(height: 24),
                
                Text(
                  'إعادة تعيين كلمة المرور',
                  style: AppTextStyles.headlineMedium,
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  'أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 32),
                
                CustomTextField(
                  controller: _emailController,
                  label: 'البريد الإلكتروني',
                  hint: 'أدخل بريدك الإلكتروني',
                  prefixIcon: MdiIcons.email,
                  keyboardType: TextInputType.emailAddress,
                  validator: Validators.email,
                ),
                
                const SizedBox(height: 24),
                
                CustomButton(
                  text: 'إرسال رابط الإعادة',
                  onPressed: _handleResetPassword,
                  isLoading: _isLoading,
                ),
                
                const SizedBox(height: 16),
                
                TextButton(
                  onPressed: () => context.pop(),
                  child: Text(
                    'العودة لتسجيل الدخول',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleResetPassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final authController = ref.read(authControllerProvider.notifier);
      
      final result = await authController.resetPassword(
        _emailController.text.trim(),
      );

      if (result.isSuccess) {
        if (mounted) {
          SnackBarUtils.showSuccess(
            context,
            result.message ?? 'تم إرسال رابط إعادة التعيين',
          );
          context.pop();
        }
      } else {
        if (mounted) {
          SnackBarUtils.showError(
            context,
            result.error ?? 'فشل في إرسال رابط الإعادة',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showError(
          context,
          'حدث خطأ غير متوقع',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
