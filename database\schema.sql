-- قاعدة بيانات تطبيق مراسِل (Morasel App Database Schema)
-- Database Schema for Morasel Corporate Communication App

-- إنشاء قاعدة البيانات
CREATE DATABASE morasel_db;
USE morasel_db;

-- ===================================
-- جداول النظام الأساسية
-- ===================================

-- جدول الأقسام
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    manager_id INT,
    parent_department_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    INDEX idx_manager (manager_id),
    INDEX idx_parent (parent_department_id)
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    profile_photo VARCHAR(500),
    role ENUM('admin', 'department_manager', 'administrator', 'employee') NOT NULL,
    department_id INT,
    employee_id VARCHAR(50),
    position VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (department_id) REFERENCES departments(id),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_department (department_id),
    INDEX idx_active (is_active)
);

-- إضافة المفتاح الخارجي للمدير في جدول الأقسام
ALTER TABLE departments 
ADD FOREIGN KEY (manager_id) REFERENCES users(id),
ADD FOREIGN KEY (parent_department_id) REFERENCES departments(id);

-- جدول إعدادات المستخدمين
CREATE TABLE user_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_setting (user_id, setting_key),
    INDEX idx_user_setting (user_id, setting_key)
);

-- ===================================
-- جداول الأمان والمصادقة
-- ===================================

-- جدول إعادة تعيين كلمات المرور
CREATE TABLE password_resets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
);

-- جدول محاولات تسجيل الدخول
CREATE TABLE login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    email VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason VARCHAR(100),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_email (email),
    INDEX idx_ip (ip_address),
    INDEX idx_timestamp (timestamp)
);

-- جدول جلسات المستخدمين
CREATE TABLE user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    device_info TEXT,
    ip_address VARCHAR(45),
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (session_token),
    INDEX idx_user (user_id),
    INDEX idx_expires (expires_at)
);

-- جدول التحقق بخطوتين
CREATE TABLE two_factor_auth (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    secret_key VARCHAR(255) NOT NULL,
    backup_codes JSON,
    is_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_2fa (user_id)
);

-- ===================================
-- جداول المراسلة والمحادثات
-- ===================================

-- جدول المجموعات
CREATE TABLE groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type ENUM('department', 'project', 'team', 'custom') NOT NULL,
    created_by INT NOT NULL,
    department_id INT,
    is_private BOOLEAN DEFAULT FALSE,
    max_members INT DEFAULT 100,
    group_photo VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (department_id) REFERENCES departments(id),
    INDEX idx_type (type),
    INDEX idx_creator (created_by),
    INDEX idx_department (department_id)
);

-- جدول أعضاء المجموعات
CREATE TABLE group_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_id INT NOT NULL,
    user_id INT NOT NULL,
    role ENUM('admin', 'moderator', 'member') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    added_by INT,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id),
    UNIQUE KEY unique_group_member (group_id, user_id),
    INDEX idx_group (group_id),
    INDEX idx_user (user_id)
);

-- جدول المحادثات
CREATE TABLE conversations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('direct', 'group') NOT NULL,
    group_id INT NULL,
    created_by INT NOT NULL,
    last_message_id INT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (group_id) REFERENCES groups(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_type (type),
    INDEX idx_group (group_id),
    INDEX idx_last_activity (last_activity)
);

-- جدول مشاركي المحادثات المباشرة
CREATE TABLE conversation_participants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id INT NOT NULL,
    user_id INT NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_conversation_participant (conversation_id, user_id),
    INDEX idx_conversation (conversation_id),
    INDEX idx_user (user_id)
);

-- جدول الرسائل
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    conversation_id INT NOT NULL,
    sender_id INT NOT NULL,
    message_type ENUM('text', 'image', 'file', 'audio', 'video', 'link', 'system') NOT NULL,
    content TEXT,
    attachment_url VARCHAR(500),
    attachment_name VARCHAR(255),
    attachment_size BIGINT,
    reply_to_message_id INT NULL,
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP NULL,
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (reply_to_message_id) REFERENCES messages(id),
    INDEX idx_conversation (conversation_id),
    INDEX idx_sender (sender_id),
    INDEX idx_sent_at (sent_at),
    INDEX idx_type (message_type),
    INDEX idx_scheduled (scheduled_at)
);

-- تحديث المفتاح الخارجي في جدول المحادثات
ALTER TABLE conversations 
ADD FOREIGN KEY (last_message_id) REFERENCES messages(id);

-- جدول قراءة الرسائل
CREATE TABLE message_reads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    user_id INT NOT NULL,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_message_read (message_id, user_id),
    INDEX idx_message (message_id),
    INDEX idx_user (user_id)
);

-- جدول ردود الفعل على الرسائل
CREATE TABLE message_reactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    user_id INT NOT NULL,
    reaction_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_message_reaction (message_id, user_id, reaction_type),
    INDEX idx_message (message_id),
    INDEX idx_user (user_id)
);

-- جدول الإشارات (Mentions)
CREATE TABLE mentions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    mentioned_user_id INT NOT NULL,
    mentioned_by_user_id INT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (mentioned_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (mentioned_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_mentioned_user (mentioned_user_id),
    INDEX idx_message (message_id)
);

-- ===================================
-- جداول الاجتماعات
-- ===================================

-- جدول الاجتماعات
CREATE TABLE meetings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    organizer_id INT NOT NULL,
    meeting_type ENUM('private', 'department', 'public') NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    timezone VARCHAR(50) DEFAULT 'UTC',
    meeting_url VARCHAR(500),
    meeting_password VARCHAR(100),
    max_participants INT DEFAULT 50,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern JSON,
    status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organizer_id) REFERENCES users(id),
    INDEX idx_organizer (organizer_id),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status),
    INDEX idx_type (meeting_type)
);

-- جدول مشاركي الاجتماعات
CREATE TABLE meeting_participants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    meeting_id INT NOT NULL,
    user_id INT NOT NULL,
    invitation_status ENUM('pending', 'accepted', 'declined', 'maybe') DEFAULT 'pending',
    attendance_status ENUM('absent', 'present', 'late') NULL,
    joined_at TIMESTAMP NULL,
    left_at TIMESTAMP NULL,
    invited_by INT NOT NULL,
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (meeting_id) REFERENCES meetings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id),
    UNIQUE KEY unique_meeting_participant (meeting_id, user_id),
    INDEX idx_meeting (meeting_id),
    INDEX idx_user (user_id),
    INDEX idx_status (invitation_status)
);

-- ===================================
-- جداول البث المباشر
-- ===================================

-- جدول البث المباشر
CREATE TABLE live_streams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    streamer_id INT NOT NULL,
    stream_type ENUM('department', 'public', 'private') NOT NULL,
    stream_url VARCHAR(500),
    stream_key VARCHAR(255),
    thumbnail_url VARCHAR(500),
    status ENUM('scheduled', 'live', 'ended', 'cancelled') DEFAULT 'scheduled',
    scheduled_start TIMESTAMP NULL,
    actual_start TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    max_viewers INT DEFAULT 1000,
    current_viewers INT DEFAULT 0,
    total_views INT DEFAULT 0,
    is_recorded BOOLEAN DEFAULT FALSE,
    recording_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (streamer_id) REFERENCES users(id),
    INDEX idx_streamer (streamer_id),
    INDEX idx_status (status),
    INDEX idx_type (stream_type),
    INDEX idx_scheduled (scheduled_start)
);

-- جدول مشاهدي البث المباشر
CREATE TABLE stream_viewers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    stream_id INT NOT NULL,
    user_id INT NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    left_at TIMESTAMP NULL,
    total_watch_time INT DEFAULT 0,
    
    FOREIGN KEY (stream_id) REFERENCES live_streams(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_stream (stream_id),
    INDEX idx_user (user_id)
);

-- جدول رسائل البث المباشر
CREATE TABLE stream_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    stream_id INT NOT NULL,
    user_id INT NOT NULL,
    message TEXT NOT NULL,
    message_type ENUM('chat', 'question', 'reaction') DEFAULT 'chat',
    is_highlighted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (stream_id) REFERENCES live_streams(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_stream (stream_id),
    INDEX idx_user (user_id),
    INDEX idx_created (created_at)
);

-- ===================================
-- جداول المهام والإشعارات
-- ===================================

-- جدول المهام
CREATE TABLE tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    assigned_by INT NOT NULL,
    assigned_to INT NOT NULL,
    conversation_id INT,
    message_id INT,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    due_date TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (assigned_by) REFERENCES users(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    FOREIGN KEY (conversation_id) REFERENCES conversations(id),
    FOREIGN KEY (message_id) REFERENCES messages(id),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_assigned_by (assigned_by),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date),
    INDEX idx_conversation (conversation_id)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type ENUM('message', 'mention', 'task', 'meeting', 'stream', 'system') NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    related_id INT,
    related_type VARCHAR(50),
    is_read BOOLEAN DEFAULT FALSE,
    is_push_sent BOOLEAN DEFAULT FALSE,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_type (type),
    INDEX idx_read (is_read),
    INDEX idx_created (created_at),
    INDEX idx_related (related_type, related_id)
);

-- جدول إعدادات الإشعارات
CREATE TABLE notification_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    notification_type VARCHAR(50) NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,
    email_enabled BOOLEAN DEFAULT FALSE,
    sound_enabled BOOLEAN DEFAULT TRUE,
    vibration_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_notification_type (user_id, notification_type),
    INDEX idx_user (user_id)
);

-- ===================================
-- جداول الملفات والوسائط
-- ===================================

-- جدول الملفات
CREATE TABLE files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_url VARCHAR(500),
    file_type VARCHAR(100) NOT NULL,
    mime_type VARCHAR(100),
    file_size BIGINT NOT NULL,
    uploaded_by INT NOT NULL,
    conversation_id INT,
    message_id INT,
    is_public BOOLEAN DEFAULT FALSE,
    download_count INT DEFAULT 0,
    thumbnail_url VARCHAR(500),
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    FOREIGN KEY (conversation_id) REFERENCES conversations(id),
    FOREIGN KEY (message_id) REFERENCES messages(id),
    INDEX idx_uploader (uploaded_by),
    INDEX idx_conversation (conversation_id),
    INDEX idx_type (file_type),
    INDEX idx_created (created_at)
);

-- جدول مشاركة الملفات
CREATE TABLE file_shares (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_id INT NOT NULL,
    shared_by INT NOT NULL,
    shared_with INT,
    share_type ENUM('user', 'group', 'department', 'public') NOT NULL,
    target_id INT,
    permissions JSON,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_by) REFERENCES users(id),
    FOREIGN KEY (shared_with) REFERENCES users(id),
    INDEX idx_file (file_id),
    INDEX idx_shared_by (shared_by),
    INDEX idx_shared_with (shared_with),
    INDEX idx_type (share_type)
);

-- ===================================
-- جداول النسخ الاحتياطي والسجلات
-- ===================================

-- جدول النسخ الاحتياطية
CREATE TABLE backups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backup_type ENUM('full', 'incremental', 'differential') NOT NULL,
    backup_scope ENUM('all', 'messages', 'files', 'users', 'settings') NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    status ENUM('pending', 'in_progress', 'completed', 'failed') DEFAULT 'pending',
    started_by INT,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    error_message TEXT,
    metadata JSON,

    FOREIGN KEY (started_by) REFERENCES users(id),
    INDEX idx_type (backup_type),
    INDEX idx_status (status),
    INDEX idx_started (started_at)
);

-- جدول سجل النشاطات
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id INT,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created (created_at)
);

-- ===================================
-- جداول الصلاحيات والإعدادات
-- ===================================

-- جدول الصلاحيات
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50),
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_category (category),
    INDEX idx_name (name)
);

-- جدول صلاحيات الأدوار
CREATE TABLE role_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role VARCHAR(50) NOT NULL,
    permission_id INT NOT NULL,
    is_granted BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_permission (role, permission_id),
    INDEX idx_role (role),
    INDEX idx_permission (permission_id)
);

-- جدول صلاحيات المستخدمين المخصصة
CREATE TABLE user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    permission_id INT NOT NULL,
    is_granted BOOLEAN DEFAULT TRUE,
    granted_by INT,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    UNIQUE KEY unique_user_permission (user_id, permission_id),
    INDEX idx_user (user_id),
    INDEX idx_permission (permission_id)
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- ===================================
-- إدراج البيانات الأساسية
-- ===================================

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (name, description, category, is_system) VALUES
('user.create', 'إنشاء مستخدمين جدد', 'user_management', TRUE),
('user.edit', 'تعديل بيانات المستخدمين', 'user_management', TRUE),
('user.delete', 'حذف المستخدمين', 'user_management', TRUE),
('user.view_all', 'عرض جميع المستخدمين', 'user_management', TRUE),
('group.create', 'إنشاء مجموعات جديدة', 'group_management', TRUE),
('group.edit', 'تعديل المجموعات', 'group_management', TRUE),
('group.delete', 'حذف المجموعات', 'group_management', TRUE),
('meeting.create_public', 'إنشاء اجتماعات عامة', 'meeting_management', TRUE),
('stream.create', 'إنشاء بث مباشر', 'stream_management', TRUE),
('backup.create', 'إنشاء نسخ احتياطية', 'system_management', TRUE),
('backup.restore', 'استرجاع النسخ الاحتياطية', 'system_management', TRUE),
('system.settings', 'إدارة إعدادات النظام', 'system_management', TRUE),
('reports.view', 'عرض التقارير', 'reporting', TRUE),
('logs.view', 'عرض سجل النشاطات', 'system_management', TRUE);

-- إدراج صلاحيات الأدوار
INSERT INTO role_permissions (role, permission_id)
SELECT 'admin', id FROM permissions WHERE is_system = TRUE;

INSERT INTO role_permissions (role, permission_id)
SELECT 'department_manager', id FROM permissions
WHERE name IN ('user.view_all', 'group.create', 'group.edit', 'meeting.create_public', 'reports.view');

INSERT INTO role_permissions (role, permission_id)
SELECT 'administrator', id FROM permissions
WHERE name IN ('group.create', 'group.edit', 'reports.view');

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app.name', 'مراسِل', 'string', 'اسم التطبيق', TRUE),
('app.version', '1.0.0', 'string', 'إصدار التطبيق', TRUE),
('backup.auto_enabled', 'true', 'boolean', 'تفعيل النسخ الاحتياطي التلقائي', FALSE),
('backup.frequency', 'daily', 'string', 'تكرار النسخ الاحتياطي', FALSE),
('max_file_size', '50', 'number', 'الحد الأقصى لحجم الملف (MB)', FALSE),
('session_timeout', '24', 'number', 'انتهاء صلاحية الجلسة (ساعات)', FALSE),
('max_login_attempts', '5', 'number', 'الحد الأقصى لمحاولات تسجيل الدخول', FALSE),
('2fa_required', 'false', 'boolean', 'إجبارية التحقق بخطوتين', FALSE);

-- ===================================
-- إنشاء الفهارس المحسنة
-- ===================================

-- فهارس مركبة للأداء
CREATE INDEX idx_messages_conversation_sent ON messages(conversation_id, sent_at DESC);
CREATE INDEX idx_notifications_user_unread ON notifications(user_id, is_read, created_at DESC);
CREATE INDEX idx_activity_logs_user_date ON activity_logs(user_id, created_at DESC);
CREATE INDEX idx_files_uploader_date ON files(uploaded_by, created_at DESC);
CREATE INDEX idx_meetings_organizer_start ON meetings(organizer_id, start_time);

-- ===================================
-- إنشاء المشاهدات (Views)
-- ===================================

-- مشاهدة للمحادثات مع آخر رسالة
CREATE VIEW conversation_summary AS
SELECT
    c.id,
    c.type,
    c.group_id,
    g.name as group_name,
    c.last_activity,
    m.content as last_message,
    m.message_type as last_message_type,
    u.name as last_sender_name,
    (SELECT COUNT(*) FROM messages WHERE conversation_id = c.id) as message_count
FROM conversations c
LEFT JOIN groups g ON c.group_id = g.id
LEFT JOIN messages m ON c.last_message_id = m.id
LEFT JOIN users u ON m.sender_id = u.id
WHERE c.is_active = TRUE;

-- مشاهدة للإشعارات غير المقروءة
CREATE VIEW unread_notifications AS
SELECT
    n.*,
    u.name as user_name
FROM notifications n
JOIN users u ON n.user_id = u.id
WHERE n.is_read = FALSE
ORDER BY n.created_at DESC;
