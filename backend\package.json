{"name": "morasel-backend", "version": "1.0.0", "description": "Backend API for Morasel corporate communication app", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "db:migrate": "sequelize-cli db:migrate", "db:seed": "sequelize-cli db:seed:all", "db:reset": "sequelize-cli db:migrate:undo:all && npm run db:migrate && npm run db:seed", "build": "babel src -d dist", "docker:build": "docker build -t morasel-backend .", "docker:run": "docker run -p 3000:3000 morasel-backend"}, "keywords": ["nodejs", "express", "api", "corporate", "communication", "messaging", "real-time"], "author": "Morasel Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "crypto": "^1.0.1", "uuid": "^9.0.1", "pg": "^8.11.3", "sequelize": "^6.35.1", "sequelize-cli": "^6.6.2", "redis": "^4.6.10", "ioredis": "^5.3.2", "bull": "^4.12.2", "nodemailer": "^6.9.7", "twilio": "^4.19.3", "firebase-admin": "^11.11.1", "sharp": "^0.32.6", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "archiver": "^6.0.1", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "lodash": "^4.17.21", "validator": "^13.11.0", "sanitize-html": "^2.11.0", "xss": "^1.0.14", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "node-cron": "^3.0.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-openapi-validator": "^5.1.2"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8", "@types/node": "^20.9.4", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.1.0", "cross-env": "^7.0.3", "concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/**", "!src/migrations/**", "!src/seeders/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/tests/**/*.test.js", "**/src/**/*.test.js"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "lint-staged": {"src/**/*.js": ["eslint --fix", "prettier --write", "git add"]}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "18"}}]]}, "eslintConfig": {"extends": ["airbnb-base"], "env": {"node": true, "jest": true}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error", "no-var": "error"}}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 80}}